/* 轮播图样式 - 从 index2.html 提取 */

/* 轮播图变量定义 */
#bannerswiper_65ae17ec95314 {
    --swipers-video-font: "Arial";
    /*字体*/
    --swipers-background-color: antiquewhite;
    /*背景颜色*/
    --swipers-slogan-size: 22px;
    /*标语字号*/
    --swipers-slogan-color: #fff;
    /*标语字体颜色*/
    --swipers-slogan-font: "Arial";
    /*标语字体*/
    --swipers-slogan-italic: normal;
    /*标语倾斜*/
    --swipers-slogan-bold: normal;
    /*标语加粗*/
    --swipers-slogan-visible: normal;
    /*标语的显示或隐藏*/

    --swipers-title-size: 60px;
    /*标题字号*/
    --swipers-title-color: #fff;
    /*标题字体颜色*/
    --swipers-title-font: "Arial";
    /*标题字体*/
    --swipers-title-italic: normal;
    /*标题倾斜*/
    --swipers-title-bold: 900;
    /*标题加粗*/
    --swipers-title-visible: normal;
    /*标题的显示或隐藏*/

    --swipers-describe-size: 22px;
    /*内容字号*/
    --swipers-describe-color: #fff;
    /*内容字体颜色*/
    --swipers-describe-font: "Arial";
    /*内容字体*/
    --swipers-describe-italic: normal;
    /*内容倾斜*/
    --swipers-describe-bold: normal;
    /*内容加粗*/
    --swipers-describe-visible: normal;
    /*内容的显示或隐藏*/
    
    --swipers-main-size: 16px;
    --swipers-main-italic: normal;
    --swipers-main-bold: normal;
    --swipers-main-color: #fff;
    --swipers-main-hoverColor: #000;
    --swipers-main-borderColor: #000;
    --swipers-main-backgroundColor: #000;
    /*按钮颜色*/
    --swipers-main-shape: 5px;
    /*按钮形状*/
    --swipers-main-hide: inline-block;
    --swipers-main-font: "Arial";

    /* 视频、图片 */
    --swipers-video-color: transparent;
    --swipers-video-to: transparent;
    --swipers-video-hide: block;
    --swipers-photo-color: transparent;
    --swipers-photo-to: transparent;
    --swipers-photo-hide: block;

    --swipers-color: #000;
    --swipers-background-color: #fff;
    --swipers-appearanceColor-color: rgba(127, 121, 121, 1);

    --swipers-highlight-color: rgba(0, 0, 0, .3);
    /*背景颜色*/
    --swipers-highlight-size: 16px;
    /*标语字号*/
    --swipers-text-color: #fff;
    /*标语字体颜色*/
    --swipers-highlight-font: "Arial";
    /*标语字体*/
    --swipers-highlight-italic: normal;
    /*标语倾斜*/
    --swipers-highlight-bold: normal;
    /*标语加粗*/
    --cursor-box-width: 180px;
    /*标语倾斜*/
    --cursor-box-height: 180px;
    /*标语加粗*/
    --swipers-highlight-visible: none;
    /*标语的显示或隐藏*/
    /* 高度范围 */
    --poster-scale-scale: 1;
}

/* 轮播图基础样式 */
.bannerswiper-second {
    position: relative;
    width: 100%;
    z-index: 1;
}

.bannerswiper-second .button-box a:hover {
    /*opacity: 0.8;*/
}

.bannerswiper-second .mainbox {
    box-sizing: border-box;
    /* padding: 40px 40px 40px; */
    margin: 0px auto;
    width: 100%;
    min-height: calc(42.7083vw * var(--poster-scale-scale));
    /* max-width: calc(var(--product-detail-style-ratio) * 75% + 80px); */
    background: var(--common-body-font-emphasisColor);
}

.bannerswiper-second .swiper-slide {
    /* height: 42.7083vw; */
}

.bannerswiper-second .slide_wraper {
    position: absolute;
    width: 100%;
    height: 100%;
    overflow: hidden;
    /*pointer-events: none;*/
    z-index: 1;
}

.bannerswiper-second .img-a {
    display: block;
    width: 100%;
    height: 100%;
}

.bannerswiper-second .slide_wraper .swiper-view::after {
    pointer-events: auto;
    content: '';
    width: 100%;
    height: 100%;
    position: absolute;
    left: 0;
    top: 0;
    z-index: 999;
    background: linear-gradient(to right, var(--swipers-video-color), var(--swipers-video-to));
}

.bannerswiper-second .slide_wraper .swiper-view {
    position: absolute;
    top: 0%;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    z-index: 1;
    display: var(--swipers-video-hide);
}

.bannerswiper-second .slide_wraper .swiper-view iframe {
    position: absolute;
    left: 50%;
    top: 50%;
    width: 300%;
    height: 200%;
    max-width: none;
    max-height: none;
    min-width: 0;
    min-height: 0;
    transform: translate(-50%, -50%);
    z-index: 1;
}

.bannerswiper-second .slide_wraper .swiper-view iframe.notfill {
    width: 100%;
    height: 100%;
}

.bannerswiper-second .imgs-lists {
    width: 100%;
    height: 100%;
    position: absolute;
    left: 0;
    top: 0;
    display: var(--swipers-photo-hide);
    background-blend-mode: multiply;
    z-index: 1;
}

.bannerswiper-second .abouts-imgs {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: no-repeat center center / cover #f5f5f5;
    transition: transform 1s ease;
    object-fit: cover;
    filter: blur(0);
    image-rendering: -webkit-optimize-contrast;
    image-rendering: optimize-contrast;
}
