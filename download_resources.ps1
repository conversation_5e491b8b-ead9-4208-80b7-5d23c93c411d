# 资源下载脚本
# 用于下载index2.html中的远程资源到本地

Write-Host "Starting download of remote resources..." -ForegroundColor Green

# 创建目录（如果不存在）
$directories = @("assets\css", "assets\js", "assets\fonts", "assets\images", "assets\icons")
foreach ($dir in $directories) {
    if (!(Test-Path $dir)) {
        New-Item -ItemType Directory -Path $dir -Force
        Write-Host "Created directory: $dir" -ForegroundColor Yellow
    }
}

# CSS文件列表
$cssFiles = @{
    "https://cdn.2cshop.com/themes/default/ksh/static/member_css/swiper.min.css" = "assets\css\swiper.min.css"
    "https://cdn.2cshop.com/themes/default/ksh/static/js/layui/css/layui.css" = "assets\css\layui.css"
    "https://cdn.2cshop.com/themes/default/ksh/static/css/stylekf.css" = "assets\css\stylekf.css"
    "https://cdn.2cshop.com/themes/default/ksh/static/css/kficon.css" = "assets\css\kficon.css"
    "https://cdn.2cshop.com/ksh/static/css/index/header/header_first.css" = "assets\css\header_first.css"
    "https://cdn.2cshop.com/ksh/static/font/bootstrap-icons.css" = "assets\css\bootstrap-icons.css"
    "https://cdn.2cshop.com/ksh/static/css/swiper-bundle.min.css" = "assets\css\swiper-bundle.min.css"
    "https://cdn.2cshop.com/ksh/static/css/ck-content.css" = "assets\css\ck-content.css"
    "https://cdn.2cshop.com/ksh/static/css/index/deliverypayment/deliveryPayment1.css" = "assets\css\deliveryPayment1.css"
    "https://cdn.2cshop.com/ksh/static/iconfont/iconfont.css" = "assets\css\iconfont2.css"
    "https://cdn.2cshop.com/ksh/static/css/index/bottom/footer_first.css" = "assets\css\footer_first.css"
    "https://cdn.2cshop.com/themes/default/ksh/static/css/chat.css" = "assets\css\chat.css"
    "https://cdn.2cshop.com/themes/default/static/css/swiper.min.css" = "assets\css\swiper2.min.css"
}

# JavaScript文件列表
$jsFiles = @{
    "https://cdn.2cshop.com/themes/default/ksh/static/js/css3-mediaqueries.js" = "assets\js\css3-mediaqueries.js"
    "https://cdn.2cshop.com/ksh/static/js/triggerButtonColor.js" = "assets\js\triggerButtonColor.js"
    "https://cdn.2cshop.com/ksh/static/js/language.js" = "assets\js\language.js"
    "https://cdn.2cshop.com/ksh/static/js/youtube_player_api.js" = "assets\js\youtube_player_api.js"
    "https://cdn.2cshop.com/ksh/static/js/swiper-bundle.min.js" = "assets\js\swiper-bundle.min.js"
    "https://cdn.2cshop.com/ksh/static/js/bannerswiper-third-main.js" = "assets\js\bannerswiper-third-main.js"
    "https://cdn.2cshop.com/themes/default/ksh/static/js/layui/layui.js" = "assets\js\layui.js"
    "https://cdn.2cshop.com/themes/default/ksh/static/js/jquery.validate.min.js" = "assets\js\jquery.validate.min.js"
    "https://cdn.2cshop.com/themes/default/ksh/static/js/jquery.cookie.js" = "assets\js\jquery.cookie.js"
    "https://cdn.2cshop.com/themes/default/ksh/static/js/common.js" = "assets\js\common.js"
}

# 常用字体文件列表
$fontFiles = @{
    "https://cdn.2cshop.com/themes/default/ksh/static/font/Roboto.ttf" = "assets\fonts\Roboto.ttf"
    "https://cdn.2cshop.com/themes/default/ksh/static/font/Open_Sans.ttf" = "assets\fonts\Open_Sans.ttf"
    "https://cdn.2cshop.com/themes/default/ksh/static/font/Lato.ttf" = "assets\fonts\Lato.ttf"
    "https://cdn.2cshop.com/themes/default/ksh/static/font/Montserrat.ttf" = "assets\fonts\Montserrat.ttf"
    "https://cdn.2cshop.com/themes/default/ksh/static/font/Poppins.ttf" = "assets\fonts\Poppins.ttf"
}

# 下载函数
function Download-File {
    param(
        [string]$Url,
        [string]$OutputPath
    )
    
    try {
        Write-Host "Downloading: $Url -> $OutputPath" -ForegroundColor Cyan
        Invoke-WebRequest -Uri $Url -OutFile $OutputPath -ErrorAction Stop
        Write-Host "Success: $OutputPath" -ForegroundColor Green
    }
    catch {
        Write-Host "Failed: $Url" -ForegroundColor Red
        Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# Download CSS files
Write-Host "`nDownloading CSS files..." -ForegroundColor Yellow
foreach ($url in $cssFiles.Keys) {
    Download-File -Url $url -OutputPath $cssFiles[$url]
}

# Download JavaScript files
Write-Host "`nDownloading JavaScript files..." -ForegroundColor Yellow
foreach ($url in $jsFiles.Keys) {
    Download-File -Url $url -OutputPath $jsFiles[$url]
}

# Download font files
Write-Host "`nDownloading font files..." -ForegroundColor Yellow
foreach ($url in $fontFiles.Keys) {
    Download-File -Url $url -OutputPath $fontFiles[$url]
}

Write-Host "`nResource download completed!" -ForegroundColor Green
Write-Host "Please check the assets directory for downloaded files." -ForegroundColor Yellow

# Show download statistics
$totalFiles = $cssFiles.Count + $jsFiles.Count + $fontFiles.Count
Write-Host "`nDownload Statistics:" -ForegroundColor Magenta
Write-Host "CSS files: $($cssFiles.Count)" -ForegroundColor White
Write-Host "JavaScript files: $($jsFiles.Count)" -ForegroundColor White
Write-Host "Font files: $($fontFiles.Count)" -ForegroundColor White
Write-Host "Total: $totalFiles files" -ForegroundColor White
