/* 自定义样式 - 从 index2.html 提取的内联样式 */

/* 字体声明 */
@font-face {
    font-family: 'Roboto_Condensed';
    src: url('../fonts/Roboto_Condensed.ttf') format('truetype');
    font-weight: normal;
    font-style: normal;
    font-display: swap;
}

/* CSS变量定义 */
:root {
    --common-body-font-family: Roboto_Condensed;
    --common-body-font-color: rgba(33, 36, 39, 1);
    --common-body-font-emphasisColor: #121212FF;
    --common-body-background-color: #F1F2F6FF;
    --common-body-background-color-to: #F1F2F6FF;
    --common-button-font-color: #fff;
}

/* 基础样式 */
body {
    background: var(--common-body-background-color);
}

/* IE兼容性样式 */
article, aside, dialog, footer, header, section, nav, figure, menu, main {
    display: block;
}

/* 按钮悬停效果 */
.solid-btn-hover {
    position: relative;
}

:root {
    --solid-btn-hover-bgcolor: rgba(0, 0, 0, .3);
}

.solid-btn-hover:after {
    content: "";
    width: 100%;
    height: 100%;
    z-index: -1;
    background: var(--solid-btn-hover-bgcolor);
    position: absolute;
    left: 0;
    top: 0;
    border-radius: inherit;
}

/* Goober动画样式 */
@keyframes go2264125279 {
    from {
        transform: scale(0) rotate(45deg);
        opacity: 0;
    }
    to {
        transform: scale(1) rotate(45deg);
        opacity: 1;
    }
}

@keyframes go3020080000 {
    from {
        transform: scale(0);
        opacity: 0;
    }
    to {
        transform: scale(1);
        opacity: 1;
    }
}

@keyframes go463499852 {
    from {
        transform: scale(0) rotate(90deg);
        opacity: 0;
    }
    to {
        transform: scale(1) rotate(90deg);
        opacity: 1;
    }
}

@keyframes go1268368563 {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

@keyframes go1310225428 {
    from {
        transform: scale(0) rotate(45deg);
        opacity: 0;
    }
    to {
        transform: scale(1) rotate(45deg);
        opacity: 1;
    }
}

@keyframes go651618207 {
    0% {
        height: 0;
        width: 0;
        opacity: 0;
    }
    40% {
        height: 0;
        width: 6px;
        opacity: 1;
    }
    100% {
        opacity: 1;
        height: 10px;
    }
}

@keyframes go901347462 {
    from {
        transform: scale(0.6);
        opacity: 0.4;
    }
    to {
        transform: scale(1);
        opacity: 1;
    }
}

.go4109123758 {
    z-index: 9999;
}

.go4109123758 > * {
    pointer-events: auto;
}

/* Latex样式 */
.___Latex___1nfc2_1 ._latex_1nfc2_1 {
    font: inherit;
}

/* 成功提示模态框 */
.success-prompt-model {
    position: fixed;
    top: 15%;
    left: 50%;
    transform: translate(-50%, 0);
    z-index: 20000000;
}

.success-prompt-model-cont {
    min-height: 48px;
    padding: 10px 20px;
    text-align: center;
    line-height: 48px;
    background: #f0f9eb;
    border: 1px solid #e1f3d8;
    border-radius: 4px;
    color: #67c23a;
    display: flex;
    align-items: center;
}

.success-prompt-model .icon-coupon {
    width: 20px;
    height: 14px;
    font-size: 12px;
    margin-right: 14px;
}

/* 警告提示模态框 */
.success-warn-model {
    position: fixed;
    top: 15%;
    left: 50%;
    transform: translate(-50%, 0);
    z-index: 20000000;
}

.success-warn-model-cont {
    min-height: 48px;
    padding: 10px 20px;
    text-align: center;
    line-height: 48px;
    background: #fdf6ec;
    border: 1px solid #faecd8;
    border-radius: 4px;
    color: #e6a23c;
    display: flex;
    align-items: center;
}

/* Facebook直播响应式样式 */
@media screen and (max-width: 767px) {
    .Facebooklive {
        max-width: 100%;
        height: auto;
        left: auto !important;
    }
}

/* Banner轮播样式 */
.bannerswiper-second .mainbox {
    background: #f1f2f6;
}
