# 提取剩余内联样式的脚本
# 用于继续优化 index2.html 中的内联样式

Write-Host "Starting extraction of remaining inline styles..." -ForegroundColor Green

# 检查文件是否存在
if (!(Test-Path "index2.html")) {
    Write-Host "Error: index2.html not found!" -ForegroundColor Red
    exit 1
}

# 创建备份
$backupFile = "index2_backup_$(Get-Date -Format 'yyyyMMdd_HHmmss').html"
Copy-Item "index2.html" $backupFile
Write-Host "Backup created: $backupFile" -ForegroundColor Yellow

# 统计当前的内联样式数量
$content = Get-Content "index2.html" -Raw
$styleMatches = [regex]::Matches($content, '<style[^>]*>.*?</style>', [System.Text.RegularExpressions.RegexOptions]::Singleline)
$inlineStyleCount = $styleMatches.Count

Write-Host "Found $inlineStyleCount inline style blocks" -ForegroundColor Cyan

# 显示每个样式块的大小和位置
for ($i = 0; $i -lt $styleMatches.Count; $i++) {
    $match = $styleMatches[$i]
    $size = $match.Value.Length
    $lineNumber = ($content.Substring(0, $match.Index) -split "`n").Count
    
    Write-Host "Style block $($i+1): Line $lineNumber, Size: $size characters" -ForegroundColor White
    
    # 显示样式块的前100个字符作为预览
    $preview = $match.Value.Substring(0, [Math]::Min(100, $match.Value.Length))
    $preview = $preview -replace "`n", " " -replace "`r", ""
    Write-Host "  Preview: $preview..." -ForegroundColor Gray
}

# 提供建议
Write-Host "`nRecommendations:" -ForegroundColor Magenta
Write-Host "1. Large style blocks (>1000 chars) should be extracted to separate CSS files" -ForegroundColor White
Write-Host "2. Component-specific styles should be grouped by functionality" -ForegroundColor White
Write-Host "3. Small utility styles can be combined into a utilities.css file" -ForegroundColor White

# 创建建议的CSS文件结构
$suggestedFiles = @(
    "assets/css/blog-styles.css",
    "assets/css/email-subscription-styles.css", 
    "assets/css/chat-styles.css",
    "assets/css/utilities.css",
    "assets/css/responsive-styles.css"
)

Write-Host "`nSuggested CSS files to create:" -ForegroundColor Yellow
foreach ($file in $suggestedFiles) {
    Write-Host "  - $file" -ForegroundColor White
}

# 检查是否有JavaScript中的动态样式
$jsStyleMatches = [regex]::Matches($content, 'insertAdjacentHTML.*?<style.*?</style>', [System.Text.RegularExpressions.RegexOptions]::Singleline)
if ($jsStyleMatches.Count -gt 0) {
    Write-Host "`nFound $($jsStyleMatches.Count) dynamic styles in JavaScript" -ForegroundColor Yellow
    Write-Host "These should be moved to CSS files and applied via classes" -ForegroundColor White
}

# 统计样式相关的远程链接
$remoteCSSMatches = [regex]::Matches($content, 'href="https://[^"]*\.css[^"]*"')
Write-Host "`nFound $($remoteCSSMatches.Count) remote CSS links" -ForegroundColor Cyan
Write-Host "Consider downloading these to local assets directory" -ForegroundColor White

# 生成样式提取报告
$reportContent = @"
# 样式提取报告 - $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')

## 当前状态
- 内联样式块数量: $inlineStyleCount
- 远程CSS链接数量: $($remoteCSSMatches.Count)
- 动态样式数量: $($jsStyleMatches.Count)

## 已创建的CSS文件
- custom-styles.css (基础自定义样式)
- product-styles.css (产品展示样式)
- banner-swiper-styles.css (轮播图样式)
- delivery-payment-styles.css (配送支付样式)
- footer-styles.css (页脚样式)

## 建议创建的CSS文件
$(foreach ($file in $suggestedFiles) { "- $($file.Split('/')[-1])`n" })

## 下一步行动
1. 继续提取大型内联样式块
2. 下载剩余的远程CSS文件
3. 优化JavaScript中的动态样式
4. 测试样式提取后的页面功能

## 备份文件
- $backupFile
"@

$reportContent | Out-File "style_extraction_report.md" -Encoding UTF8
Write-Host "`nReport saved to: style_extraction_report.md" -ForegroundColor Green

Write-Host "`nStyle extraction analysis completed!" -ForegroundColor Green
Write-Host "Review the report and continue with manual extraction of remaining styles." -ForegroundColor Yellow
