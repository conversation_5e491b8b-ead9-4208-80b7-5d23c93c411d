/* 产品展示样式 - 从 index2.html 提取 */

/* 产品组件变量定义 */
#procate_66ac713118ecc {
    --product-background-color: "#fff";
    --product-background-to: "#fff";
    /* 背景颜色 */

    --product-title-size: 56px;
    /*标题字号*/
    --product-title-color: #000;
    /*标题字体颜色*/
    --product-title-font: "微软雅黑";
    /*标题字体*/
    --product-title-italic: normal;
    /*标题倾斜*/
    --product-title-bold: 900;
    /*标题加粗*/
    --product-title-hide: block;
    /*标题显示隐藏*/

    --product-subtitle-size: 16px;
    /*描述字号*/
    --product-subtitle-color: #000;
    /*描述字体颜色*/
    --product-subtitle-font: "Inter";
    /*描述字体*/
    --product-subtitle-italic: normal;
    /*描述倾斜*/
    --product-subtitle-bold: 400;
    /*描述加粗*/
    --product-subtitle-hide: block;
    /*描述显示隐藏*/

    --product-classify_name-size: 26px;
    /*分类名称字号*/
    --product-classify_name-color: #000;
    /*分类名称字体颜色*/
    --product-classify_name-font: "微软雅黑";
    /*分类名称字体*/
    --product-classify_name-italic: normal;
    /*分类名称倾斜*/
    --product-classify_name-bold: normal;
    /*分类名称加粗*/
    --product-classify_name-hide: block;
    /*分类名称显示隐藏*/

    --product-classify_desc-size: 14px;
    /*分类名称字号*/
    --product-classify_desc-color: #000;
    /*分类名称字体颜色*/
    --product-classify_desc-font: "微软雅黑";
    /*分类名称字体*/
    --product-classify_desc-italic: normal;
    /*分类名称倾斜*/
    --product-classify_desc-bold: normal;
    /*分类名称加粗*/
    --product-classify_desc-hide: block;
    /*分类名称显示隐藏*/
    
    --product-button-size: 14px;
    /*分类名称字号*/
    --product-button-color: #fff;
    --product-button-hoverColor: #fff;
    /*分类名称字体颜色*/
    --product-button-font: "微软雅黑";
    /*分类名称字体*/
    --product-button-italic: normal;
    /*分类名称倾斜*/
    --product-button-bold: normal;
    /*分类名称加粗*/
    --product-button-hide: inline-block;
    --product-button-borderColor: #000;
    --product-button-backgroundColor: #000;
    --product-button-shape: 5px;
    /*分类名称显示隐藏*/

    --product-line-matters: 32vw;
    --product-number-matters: 30vw;
    --product-direction-matters: 13vw;
    --product-back-matters: 1vw;
    --product-photo-color: normal;
    --product-photo-to: normal;
    --product-classify_photo-to: transparent;
    --product-classify_photo-color: transparent;
    --product-photo-hide: block;

    /* 列表项内图片的比例 */
    --product-direction-img-padding-bottom: 70%;
    /* 列表项内图片的显示与否 */
    --product-product_cate_item_layout-imghide: block;
    /* 页面左右边距 */
    --product-back-inner-margin: 260;
    /* 列表项的宽度（列数） */
    --product-item-width: 33.333333%;
}

/* 基础重置样式 */
* {
    margin: 0px;
    padding: 0px;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
}

a {
    text-decoration: none;
    outline: none;
}

/* 产品第三方展示区域 */
.product_thirdly {
    text-align: center;
    padding: 5.8vw 0;
    background: linear-gradient(to right, var(--product-background-color), var(--product-background-to));
}

.product_thirdly-content {
    position: relative;
    margin: 0 calc(100vw / 1920 * var(--product-back-inner-margin));
}

.product_thirdly .product-title {
    width: 1200px;
    max-width: 100%;
    margin: 0 auto;
    font-size: var(--product-title-size);
    color: var(--product-title-color);
    font-family: var(--product-title-font);
    font-style: var(--product-title-italic);
    font-weight: var(--product-title-bold);
    display: var(--product-title-hide);
    line-height: 1;
    margin-bottom: 30px;
}

.product_thirdly .product-text {
    width: 1200px;
    max-width: 100%;
    margin: 0 auto;
    font-size: var(--product-subtitle-size);
    color: var(--product-subtitle-color);
    font-family: var(--product-subtitle-font);
    font-style: var(--product-subtitle-italic);
    font-weight: var(--product-subtitle-bold);
    display: var(--product-subtitle-hide);
    line-height: 1;
    margin-bottom: 60px;
}

.product_thirdly .flex {
    position: relative;
    font-size: 0;
    letter-spacing: 0;
    display: flex;
    flex-wrap: wrap;
    margin-left: -5px;
    margin-right: -5px;
}

.product_thirdly .content {
    width: var(--product-item-width);
    min-height: 16.67vw;
    padding: 0 5px;
    text-align: center;
    overflow: hidden;
    margin-bottom: 10px;
    position: relative;
}

.product_thirdly .content:nth-child(4),
.product_thirdly .content:nth-child(5) {
    min-height: 22.29vw;
}

.product_thirdly .flex .content2 {
    width: calc(100% - var(--product-item-width));
}

.product_thirdly .content .box {
    position: relative;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    cursor: pointer;
}

.product_thirdly img {
    width: 100%;
    height: 100%;
    width: 18vw;
    height: var(--product-direction-matters);
    margin-bottom: 1vw;
    object-fit: cover;
    filter: blur(0);
}

.product_thirdly .item-pic {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border-radius: var(--product-classify_photo-shape2);
    overflow: hidden;
}

.product_thirdly .item-pic .img {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: no-repeat center center / cover #f5f5f5;
    display: var(--product-classify_photo-hide);
    transition: transform 1s ease; /* 平滑过渡效果 */
    object-fit: cover;
    filter: blur(0);
    image-rendering: -webkit-optimize-contrast;
    image-rendering: optimize-contrast;
}
