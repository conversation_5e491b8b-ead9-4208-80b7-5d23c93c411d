/* 配送和支付样式 - 从 index2.html 提取 */

/* 配送支付变量定义 */
#deliverypayment_64efe56731c27 {
    --delivery-title-size: 20px;
    --delivery-featureTitle-color: #121212FF;
    --delivery-featureDescription-size: 16px;
    --delivery-featureTitle-size: 20px;
    --delivery-featureDescription-color: #121212FF;
    --delivery-title-hide: none;
    --delivery-title-visible: none;
    --delivery-featureDescription-hide: block;
    --delivery-featureDescription-visible: block;
    --delivery-background-color: #F1F2F6FF;
    --delivery-background-to: #F1F2F6FF;
    --delivery-background-style: pure;
    --delivery-title-visible2: true;
    --delivery-featureDescription-visible2: true;
    --delivery-featureDescription-bold: 400;
    --delivery-featureIcon-hide: inline-block;
    --delivery-featureIcon-visible: inline-block;
    --delivery-featureIcon-visible2: true;
    --delivery-featureDescription-font: Roboto_Condensed;
    --delivery-featureTitle-font: Roboto_Condensed;
    --delivery-title-font: Roboto_Condensed;
    --delivery-featureTitle-bold: 700;
    --delivery-button-hoverColor: rgba(255, 255, 255, 1);
}

:root {
    /* 左右边距 */
    --delivery-back-inner-margin: 260;

    /* 上下边距 */
    --delivery-padding-top_pc: 21px;
    --delivery-padding-top_pad: 15px;
    --delivery-padding-top_mobile: 25px;
    --delivery-padding-bottom_pc: 21px;
    --delivery-padding-bottom_pad: 15px;
    --delivery-padding-bottom_mobile: 25px;

    /* 列表项的宽度（列数） */
    --delivery-number-item-width: 32%;
    /* 列表项的右边距(随列数改变) */
    --delivery-number-item-margin-right: 1.333%;

    --delivery-number-item-mobile-width: 100%;
    --delivery-number-item-mobile-margin-right: 0%;
    --delivery-number-number: 3;
}

/* 配送支付基础样式 */
.delivery-first {
    padding: 0;
}

.delivery-first .delivery-content {
    margin: 0 calc(100vw / 1920 * var(--delivery-back-inner-margin));
    padding: var(--delivery-padding-top_pc) 0 var(--delivery-padding-bottom_pc);
}

.delivery-first .card {
    width: var(--delivery-number-item-width);
    margin-left: calc(var(--delivery-number-item-margin-right) * .5);
    margin-right: calc(var(--delivery-number-item-margin-right) * .5);
}

/* 平板响应式样式 */
@media only screen and (max-width: 1024px) {
    .delivery-first .delivery-content {
        margin: 0 calc(100vw / 1024 * 80);
        padding: var(--delivery-padding-top_pad) 0 var(--delivery-padding-bottom_pad);
    }
    
    .delivery-first .delivery-content.loose {
        margin: 0 calc(100vw / 1024 * 30);
    }
}

/* 移动端响应式样式 */
@media only screen and (max-width: 750px) {
    .delivery-first .delivery-content {
        margin: 0 !important;
        padding: var(--delivery-padding-top_mobile) 0 var(--delivery-padding-bottom_mobile);
    }
}
