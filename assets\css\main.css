#baguetteBox-overlay { display: none; opacity: 0; position: fixed; overflow: hidden; top: 0; left: 0; width: 100%; height: 100%; background-color: #222; background-color: rgba(0, 0, 0, 0.8); -webkit-transition: opacity .5s ease; -o-transition: opacity .5s ease; -moz-transition: opacity .5s ease; transition: opacity .5s ease; z-index: 99999999999999999999999999999999999999; }#baguetteBox-overlay.visible { opacity: 1; }#baguetteBox-overlay .full-image { display: inline-block; position: relative; width: 100%; height: 100%; text-align: center; }#baguetteBox-overlay .full-image figure { display: inline; margin: 0; height: 100%; }#baguetteBox-overlay .full-image img { display: inline-block; width: auto; height: auto; max-height: 100%; max-width: 100%; vertical-align: middle; -webkit-box-shadow: 0 0 8px rgba(0, 0, 0, 0.6); box-shadow: 0 0 8px rgba(0, 0, 0, 0.6); }#baguetteBox-overlay .full-image figcaption { display: block; position: absolute; bottom: 0; width: 100%; text-align: center; line-height: 1.8; color: #ccc; background-color: #000; background-color: rgba(0, 0, 0, 0.6); font-family: sans-serif; }#baguetteBox-overlay .full-image:before { content: ""; display: inline-block; height: 50%; width: 1px; margin-right: -1px; }#baguetteBox-slider { position: absolute; left: 0; top: 0; height: 100%; width: 100%; white-space: nowrap; -webkit-transition: left .4s ease, -webkit-transform .4s ease; transition: left .4s ease, -webkit-transform .4s ease; -o-transition: left .4s ease, -o-transform .4s ease; -moz-transition: left .4s ease, transform .4s ease, -moz-transform .4s ease; transition: left .4s ease, transform .4s ease; transition: left .4s ease, transform .4s ease, -webkit-transform .4s ease, -moz-transform .4s ease, -o-transform .4s ease; transition: left .4s ease, transform .4s ease, -webkit-transform .4s ease; }#baguetteBox-slider.bounce-from-right { -webkit-animation: bounceFromRight .4s ease-out; -moz-animation: bounceFromRight .4s ease-out; animation: bounceFromRight .4s ease-out; }#baguetteBox-slider.bounce-from-left { -webkit-animation: bounceFromLeft .4s ease-out; -moz-animation: bounceFromLeft .4s ease-out; animation: bounceFromLeft .4s ease-out; }.baguetteBox-button#next-button, .baguetteBox-button#previous-button { top: 50%; top: -webkit-calc(50% - 30px); top: -moz-calc(50% - 30px); top: calc(50% - 30px); width: 44px; height: 60px; }.baguetteBox-button { position: absolute; cursor: pointer; outline: 0; padding: 0; margin: 0; border: 0; border-radius: 15%; background-color: #323232; background-color: rgba(50, 50, 50, 0.5); color: #ddd; font: 1.6em sans-serif; -webkit-transition: background-color .4s ease; -o-transition: background-color .4s ease; -moz-transition: background-color .4s ease; transition: background-color .4s ease; }.baguetteBox-button:hover { background-color: rgba(50, 50, 50, 0.9); }.baguetteBox-button#next-button { right: 2%; display: block !important; }.baguetteBox-button#previous-button { left: 2%; display: block !important; }.baguetteBox-button#close-button { top: 20px; right: 2%; right: -webkit-calc(2% + 6px); right: -moz-calc(2% + 6px); right: calc(2% + 6px); width: 30px; height: 30px; }.baguetteBox-button svg { position: absolute; left: 0; top: 0; }


/*** Swiper 3.4.2* Most modern mobile touch slider and framework with hardware accelerated transitions** http://www.idangero.us/swiper/** Copyright 2017, Vladimir Kharlampidi* The iDangero.us* http://www.idangero.us/** Licensed under MIT** Released on: March 10, 2017*/
.swiper-container{margin-left:auto;margin-right:auto;position:relative;overflow:hidden;z-index:1}.swiper-container-no-flexbox .swiper-slide{float:left}.swiper-container-vertical>.swiper-wrapper{-webkit-box-orient:vertical;-moz-box-orient:vertical;-ms-flex-direction:column;-webkit-flex-direction:column;flex-direction:column}.swiper-wrapper{position:relative;width:100%;height:100%;z-index:1;display:-webkit-box;display:-moz-box;display:-ms-flexbox;display:-webkit-flex;display:flex;-webkit-transition-property:-webkit-transform;-moz-transition-property:-moz-transform;-o-transition-property:-o-transform;-ms-transition-property:-ms-transform;transition-property:transform;-webkit-box-sizing:content-box;-moz-box-sizing:content-box;box-sizing:content-box}.swiper-container-android .swiper-slide,.swiper-wrapper{-webkit-transform:translate3d(0,0,0);-moz-transform:translate3d(0,0,0);-o-transform:translate(0,0);-ms-transform:translate3d(0,0,0);transform:translate3d(0,0,0)}.swiper-container-multirow>.swiper-wrapper{-webkit-box-lines:multiple;-moz-box-lines:multiple;-ms-flex-wrap:wrap;-webkit-flex-wrap:wrap;flex-wrap:wrap}.swiper-container-free-mode>.swiper-wrapper{-webkit-transition-timing-function:ease-out;-moz-transition-timing-function:ease-out;-ms-transition-timing-function:ease-out;-o-transition-timing-function:ease-out;transition-timing-function:ease-out;margin:0 auto}.swiper-slide{-webkit-flex-shrink:0;-ms-flex:0 0 auto;flex-shrink:0;width:100%;height:100%;position:relative}.swiper-container-autoheight,.swiper-container-autoheight .swiper-slide{height:auto}.swiper-container-autoheight .swiper-wrapper{-webkit-box-align:start;-ms-flex-align:start;-webkit-align-items:flex-start;align-items:flex-start;-webkit-transition-property:-webkit-transform,height;-moz-transition-property:-moz-transform;-o-transition-property:-o-transform;-ms-transition-property:-ms-transform;transition-property:transform,height}.swiper-container .swiper-notification{position:absolute;left:0;top:0;pointer-events:none;opacity:0;z-index:-1000}.swiper-wp8-horizontal{-ms-touch-action:pan-y;touch-action:pan-y}.swiper-wp8-vertical{-ms-touch-action:pan-x;touch-action:pan-x}.swiper-button-next,.swiper-button-prev{position:absolute;top:50%;width:27px;height:44px;margin-top:-22px;z-index:10;cursor:pointer;-moz-background-size:27px 44px;-webkit-background-size:27px 44px;background-size:27px 44px;background-position:center;background-repeat:no-repeat}.swiper-button-next.swiper-button-disabled,.swiper-button-prev.swiper-button-disabled{opacity:.35;cursor:auto;pointer-events:none}.swiper-button-prev,.swiper-container-rtl .swiper-button-next{}.swiper-button-prev.swiper-button-white,.swiper-container-rtl .swiper-button-next.swiper-button-white{}.swiper-button-next,.swiper-container-rtl .swiper-button-prev{}.swiper-button-next.swiper-button-white,.swiper-container-rtl .swiper-button-prev.swiper-button-white{}.swiper-pagination{position:absolute;text-align:center;-webkit-transition:.3s;-moz-transition:.3s;-o-transition:.3s;transition:.3s;-webkit-transform:translate3d(0,0,0);-ms-transform:translate3d(0,0,0);-o-transform:translate3d(0,0,0);transform:translate3d(0,0,0);z-index:10}.swiper-pagination.swiper-pagination-hidden{opacity:0}.swiper-container-horizontal>.swiper-pagination-bullets,.swiper-pagination-custom,.swiper-pagination-fraction{bottom:10px;left:0;width:100%}.swiper-pagination-bullet{width:8px;height:8px;display:inline-block;border-radius:100%;background:#000;opacity:.2}button.swiper-pagination-bullet{border:none;margin:0;padding:0;box-shadow:none;-moz-appearance:none;-ms-appearance:none;-webkit-appearance:none;appearance:none}.swiper-pagination-clickable .swiper-pagination-bullet{cursor:pointer}.swiper-pagination-white .swiper-pagination-bullet{background:#fff}.swiper-pagination-bullet-active{opacity:1;background:#007aff}.swiper-pagination-white .swiper-pagination-bullet-active{background:#fff}.swiper-pagination-black .swiper-pagination-bullet-active{background:#000}.swiper-container-vertical>.swiper-pagination-bullets{right:10px;top:50%;-webkit-transform:translate3d(0,-50%,0);-moz-transform:translate3d(0,-50%,0);-o-transform:translate(0,-50%);-ms-transform:translate3d(0,-50%,0);transform:translate3d(0,-50%,0)}.swiper-container-vertical>.swiper-pagination-bullets .swiper-pagination-bullet{margin:5px 0;display:block}.swiper-container-horizontal>.swiper-pagination-bullets .swiper-pagination-bullet{margin:0 5px}.swiper-pagination-progress{background:rgba(0,0,0,.25);position:absolute}.swiper-pagination-progress .swiper-pagination-progressbar{background:#007aff;position:absolute;left:0;top:0;width:100%;height:100%;-webkit-transform:scale(0);-ms-transform:scale(0);-o-transform:scale(0);transform:scale(0);-webkit-transform-origin:left top;-moz-transform-origin:left top;-ms-transform-origin:left top;-o-transform-origin:left top;transform-origin:left top}.swiper-container-rtl .swiper-pagination-progress .swiper-pagination-progressbar{-webkit-transform-origin:right top;-moz-transform-origin:right top;-ms-transform-origin:right top;-o-transform-origin:right top;transform-origin:right top}.swiper-container-horizontal>.swiper-pagination-progress{width:100%;height:4px;left:0;top:0}.swiper-container-vertical>.swiper-pagination-progress{width:4px;height:100%;left:0;top:0}.swiper-pagination-progress.swiper-pagination-white{background:rgba(255,255,255,.5)}.swiper-pagination-progress.swiper-pagination-white .swiper-pagination-progressbar{background:#fff}.swiper-pagination-progress.swiper-pagination-black .swiper-pagination-progressbar{background:#000}.swiper-container-3d{-webkit-perspective:1200px;-moz-perspective:1200px;-o-perspective:1200px;perspective:1200px}.swiper-container-3d .swiper-cube-shadow,.swiper-container-3d .swiper-slide,.swiper-container-3d .swiper-slide-shadow-bottom,.swiper-container-3d .swiper-slide-shadow-left,.swiper-container-3d .swiper-slide-shadow-right,.swiper-container-3d .swiper-slide-shadow-top,.swiper-container-3d .swiper-wrapper{-webkit-transform-style:preserve-3d;-moz-transform-style:preserve-3d;-ms-transform-style:preserve-3d;transform-style:preserve-3d}.swiper-container-3d .swiper-slide-shadow-bottom,.swiper-container-3d .swiper-slide-shadow-left,.swiper-container-3d .swiper-slide-shadow-right,.swiper-container-3d .swiper-slide-shadow-top{position:absolute;left:0;top:0;width:100%;height:100%;pointer-events:none;z-index:10}.swiper-container-3d .swiper-slide-shadow-left{background-image:-webkit-gradient(linear,left top,right top,from(rgba(0,0,0,.5)),to(rgba(0,0,0,0)));background-image:-webkit-linear-gradient(right,rgba(0,0,0,.5),rgba(0,0,0,0));background-image:-moz-linear-gradient(right,rgba(0,0,0,.5),rgba(0,0,0,0));background-image:-o-linear-gradient(right,rgba(0,0,0,.5),rgba(0,0,0,0));background-image:linear-gradient(to left,rgba(0,0,0,.5),rgba(0,0,0,0))}.swiper-container-3d .swiper-slide-shadow-right{background-image:-webkit-gradient(linear,right top,left top,from(rgba(0,0,0,.5)),to(rgba(0,0,0,0)));background-image:-webkit-linear-gradient(left,rgba(0,0,0,.5),rgba(0,0,0,0));background-image:-moz-linear-gradient(left,rgba(0,0,0,.5),rgba(0,0,0,0));background-image:-o-linear-gradient(left,rgba(0,0,0,.5),rgba(0,0,0,0));background-image:linear-gradient(to right,rgba(0,0,0,.5),rgba(0,0,0,0))}.swiper-container-3d .swiper-slide-shadow-top{background-image:-webkit-gradient(linear,left top,left bottom,from(rgba(0,0,0,.5)),to(rgba(0,0,0,0)));background-image:-webkit-linear-gradient(bottom,rgba(0,0,0,.5),rgba(0,0,0,0));background-image:-moz-linear-gradient(bottom,rgba(0,0,0,.5),rgba(0,0,0,0));background-image:-o-linear-gradient(bottom,rgba(0,0,0,.5),rgba(0,0,0,0));background-image:linear-gradient(to top,rgba(0,0,0,.5),rgba(0,0,0,0))}.swiper-container-3d .swiper-slide-shadow-bottom{background-image:-webkit-gradient(linear,left bottom,left top,from(rgba(0,0,0,.5)),to(rgba(0,0,0,0)));background-image:-webkit-linear-gradient(top,rgba(0,0,0,.5),rgba(0,0,0,0));background-image:-moz-linear-gradient(top,rgba(0,0,0,.5),rgba(0,0,0,0));background-image:-o-linear-gradient(top,rgba(0,0,0,.5),rgba(0,0,0,0));background-image:linear-gradient(to bottom,rgba(0,0,0,.5),rgba(0,0,0,0))}.swiper-container-coverflow .swiper-wrapper,.swiper-container-flip .swiper-wrapper{-ms-perspective:1200px}.swiper-container-cube,.swiper-container-flip{overflow:visible}.swiper-container-cube .swiper-slide,.swiper-container-flip .swiper-slide{pointer-events:none;-webkit-backface-visibility:hidden;-moz-backface-visibility:hidden;-ms-backface-visibility:hidden;backface-visibility:hidden;z-index:1}.swiper-container-cube .swiper-slide .swiper-slide,.swiper-container-flip .swiper-slide .swiper-slide{pointer-events:none}.swiper-container-cube .swiper-slide-active,.swiper-container-cube .swiper-slide-active .swiper-slide-active,.swiper-container-flip .swiper-slide-active,.swiper-container-flip .swiper-slide-active .swiper-slide-active{pointer-events:auto}.swiper-container-cube .swiper-slide-shadow-bottom,.swiper-container-cube .swiper-slide-shadow-left,.swiper-container-cube .swiper-slide-shadow-right,.swiper-container-cube .swiper-slide-shadow-top,.swiper-container-flip .swiper-slide-shadow-bottom,.swiper-container-flip .swiper-slide-shadow-left,.swiper-container-flip .swiper-slide-shadow-right,.swiper-container-flip .swiper-slide-shadow-top{z-index:0;-webkit-backface-visibility:hidden;-moz-backface-visibility:hidden;-ms-backface-visibility:hidden;backface-visibility:hidden}.swiper-container-cube .swiper-slide{visibility:hidden;-webkit-transform-origin:0 0;-moz-transform-origin:0 0;-ms-transform-origin:0 0;transform-origin:0 0;width:100%;height:100%}.swiper-container-cube.swiper-container-rtl .swiper-slide{-webkit-transform-origin:100% 0;-moz-transform-origin:100% 0;-ms-transform-origin:100% 0;transform-origin:100% 0}.swiper-container-cube .swiper-slide-active,.swiper-container-cube .swiper-slide-next,.swiper-container-cube .swiper-slide-next+.swiper-slide,.swiper-container-cube .swiper-slide-prev{pointer-events:auto;visibility:visible}.swiper-container-cube .swiper-cube-shadow{position:absolute;left:0;bottom:0;width:100%;height:100%;background:#000;opacity:.6;-webkit-filter:blur(50px);filter:blur(50px);z-index:0}.swiper-container-fade.swiper-container-free-mode .swiper-slide{-webkit-transition-timing-function:ease-out;-moz-transition-timing-function:ease-out;-ms-transition-timing-function:ease-out;-o-transition-timing-function:ease-out;transition-timing-function:ease-out}.swiper-container-fade .swiper-slide{pointer-events:none;-webkit-transition-property:opacity;-moz-transition-property:opacity;-o-transition-property:opacity;transition-property:opacity}.swiper-container-fade .swiper-slide .swiper-slide{pointer-events:none}.swiper-container-fade .swiper-slide-active,.swiper-container-fade .swiper-slide-active .swiper-slide-active{pointer-events:auto}.swiper-zoom-container{width:100%;height:100%;display:-webkit-box;display:-moz-box;display:-ms-flexbox;display:-webkit-flex;display:flex;-webkit-box-pack:center;-moz-box-pack:center;-ms-flex-pack:center;-webkit-justify-content:center;justify-content:center;-webkit-box-align:center;-moz-box-align:center;-ms-flex-align:center;-webkit-align-items:center;align-items:center;text-align:center}.swiper-zoom-container>canvas,.swiper-zoom-container>img,.swiper-zoom-container>svg{max-width:100%;max-height:100%;object-fit:contain}.swiper-scrollbar{border-radius:10px;position:relative;-ms-touch-action:none;background:rgba(0,0,0,.1)}.swiper-container-horizontal>.swiper-scrollbar{position:absolute;left:1%;bottom:3px;z-index:50;height:5px;width:98%}.swiper-container-vertical>.swiper-scrollbar{position:absolute;right:3px;top:1%;z-index:50;width:5px;height:98%}.swiper-scrollbar-drag{height:100%;width:100%;position:relative;background:rgba(0,0,0,.5);border-radius:10px;left:0;top:0}.swiper-scrollbar-cursor-drag{cursor:move}.swiper-lazy-preloader{width:42px;height:42px;position:absolute;left:50%;top:50%;margin-left:-21px;margin-top:-21px;z-index:10;-webkit-transform-origin:50%;-moz-transform-origin:50%;transform-origin:50%;-webkit-animation:swiper-preloader-spin 1s steps(12,end) infinite;-moz-animation:swiper-preloader-spin 1s steps(12,end) infinite;animation:swiper-preloader-spin 1s steps(12,end) infinite}.swiper-lazy-preloader:after{display:block;content:"";width:100%;height:100%;;background-position:50%;-webkit-background-size:100%;background-size:100%;background-repeat:no-repeat}.swiper-lazy-preloader-white:after{}@-webkit-keyframes swiper-preloader-spin{100%{-webkit-transform:rotate(360deg)}}@keyframes swiper-preloader-spin{100%{transform:rotate(360deg)}}

.swiper-button-prev, .swiper-container-rtl .swiper-button-next {background-image: none;left: 0px;right: auto;}
.swiper-button-next, .swiper-container-rtl .swiper-button-prev {background-image: none;right: 0px;left: auto; }
.swiper-button-next, .swiper-button-prev { font-family: "iconfont"; font-size: 18px;  text-align: center; color: #333; z-index: 10; }

@use "sass:math";
@use "sass:math";
@media screen and (min-width: 992px) { .n-nav, .n-nav-icon { display: none; } }
@-webkit-keyframes resize { from, 60%, 75%, 90%, to { -webkit-animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1); }0% { opacity: 0;-webkit-transform: scale(5, 5);transform: scale(5, 5); }100% { opacity: 1;-webkit-transform: scale(1, 1);transform: scale(1, 1); } }@-moz-keyframes resize { from, 60%, 75%, 90%, to { -webkit-animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);-moz-animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1); }0% { opacity: 0;-webkit-transform: scale(5, 5);-moz-transform: scale(5, 5);transform: scale(5, 5); }100% { opacity: 1;-webkit-transform: scale(1, 1);-moz-transform: scale(1, 1);transform: scale(1, 1); } }@keyframes resize { from, 60%, 75%, 90%, to { -webkit-animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);-moz-animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1); }0% { opacity: 0;-webkit-transform: scale(5, 5);-moz-transform: scale(5, 5);-o-transform: scale(5, 5);transform: scale(5, 5); }100% { opacity: 1;-webkit-transform: scale(1, 1);-moz-transform: scale(1, 1);-o-transform: scale(1, 1);transform: scale(1, 1); } }

.n-nav .n-nav-icon { position: fixed; right: 5px; top: 3px; width: 35px; height: 28px; line-height: 28px; text-align: center; -webkit-transition: all 0.4s ease 0s; -o-transition: all 0.4s ease 0s; -moz-transition: all 0.4s ease 0s; transition: all 0.4s ease 0s; z-index: 999999999; }

@media screen and (max-width: 767px) { .n-nav .n-nav-icon { top: 1px; } }

.n-nav .n-nav-icon .iconfont { font-size: 20px; font-size: 2.0rem; color:  #333; }

.n-nav .n-nav-icon span { display: none; width: 100%; height: 2px; margin-bottom: 4px; background-color:  var(--common-body-font-emphasisColor); }

.n-nav .n-nav-icon.active { top: 5px; background: #666 url(../fonts/nav-close.png) no-repeat center; background-size: 30px; }

.n-nav .n-nav-icon.active span, .n-nav .n-nav-icon.active .iconfont { display: none; }

.n-nav .n-nav-body { display: none; position: fixed; z-index: 99999; top: 0; bottom: 0; width: 100%; left: 0; overflow-y: scroll; background-color: #666; padding-top: 40px; }

.n-nav .box-search { background-color: #fff; padding: 5px 10px; position: relative; border: solid 1px  var(--common-body-font-emphasisColor); margin: 5px 10px; }

.n-nav .box-search .item-input { width: 100%; line-height: 32px; height: 32px; padding: 0 60px 0 0; }

.n-nav .box-search .iconfont { font-weight: bold; background-color:  var(--common-body-font-emphasisColor); width: 44px; height: 32px; line-height: 32px; border-radius: 5px; color: #fff; text-align: center; position: absolute; right: 10px; top: 5px; z-index: 0; }

.n-nav .box-search .iconfont:hover { background-color: #666; }

.n-nav .box-search .item-submit { position: absolute; width: 44px; height: 32px; right: 10px; top: 5px; z-index: 1; }

.n-nav .box-list { margin-top: 30px; }

.n-nav .box-list > li { position: relative; -webkit-animation-duration: 0.5s; -moz-animation-duration: 0.5s; animation-duration: 0.5s; -webkit-animation-fill-mode: both; -moz-animation-fill-mode: both; animation-fill-mode: both; -webkit-animation-name: resize; -moz-animation-name: resize; animation-name: resize; -webkit-transition: all 0.5s; -o-transition: all 0.5s; -moz-transition: all 0.5s; transition: all 0.5s; padding: 0 10px; }

.n-nav .box-list > li a { display: block; font-size: 14px; font-size: 1.4rem; line-height: 26px; padding: 10px 25px 10px 10px; color: #fff; text-transform: uppercase; border-bottom: 1px solid rgba(255, 255, 255, 0.3); }

.n-nav .box-list li { position: relative; }

.n-nav .box-list li .has-next { position: absolute; top: 10px; right: 10px; width: 20px; height: 20px; background: url(../fonts/n1.png) no-repeat center; background-size: contain; -webkit-transition: all .5s; -o-transition: all .5s; -moz-transition: all .5s; transition: all .5s; cursor: pointer; }

.n-nav .box-list li.active > .has-next { background: url(../fonts/n2.png) no-repeat center; background-size: contain; }

.n-nav .box-list .yiji-list { display: none; }

.n-nav .box-list .yiji-list li { padding-left: 12px; }

.n-nav .box-list .yiji-list li .has-next { right: 0; }

.n-nav .box-list .yiji-list li > a { padding: 10px 25px 10px 20px; line-height: 22px; position: relative; }

.n-nav .box-list .yiji-list li > a:before { position: absolute; content: ''; width: 5px; height: 5px; background-color: #fff; top: 19px; left: 10px; }

.n-nav .box-list .erji-list { display: none; }

.n-nav .box-list .sanji-list { display: none; }

/* @media only screen and (max-width: 750px) {
	.nav_first .searchs {
		height: calc(100vh - 52px) !important;
	}

	.nav_second .searchs {
		height: calc(100vh - 52px) !important;
	}
	.nav_third .searchs {
		height: calc(100vh - 52px) !important;
	}

	.nav_four .searchs {
		height: calc(100vh - 52px) !important;
	}
	.nav_five .searchs {
		height: calc(100vh - 52px) !important;
	}
} */
@use "sass:math";
@use "sass:math";
.slideshow { position: relative; z-index: 0; }
.slideshow .wp { position: absolute; left: 50%; margin-left: -600px; top: 50%; margin-top: -166.5px; }
.slideshow .wp img { max-width: 100%; max-height: 80%; }
.slideshow .item a img { width: 100%; -webkit-transition: all 6s ease-out; -o-transition: all 6s ease-out; -moz-transition: all 6s ease-out; transition: all 6s ease-out; -webkit-transform: scale(1.1); -moz-transform: scale(1.1); -ms-transform: scale(1.1); -o-transform: scale(1.1); transform: scale(1.1); }
.slideshow .item.slick-active img { -webkit-transform: scale(1); -moz-transform: scale(1); -ms-transform: scale(1); -o-transform: scale(1); transform: scale(1); }
.slick-dots { position: absolute; left: 0; bottom: 3.86667%; width: 100%; text-align: center; font-size: 0; letter-spacing: 0;}
.slick-dots li { display: inline-block; vertical-align: bottom; cursor: pointer; -webkit-transition: all .35s; -o-transition: all .35s; -moz-transition: all .35s; transition: all .35s; font-size: 0; width: 12px; height: 12px; border-radius: 50%; margin: 0 7px; background-color: rgba(255,255,255,.8); border: solid 1px #FFFFFF; }
.slick-dots li.slick-active { background-color: transparent; }
@media screen and (max-width: 1259px) { .slick-dots li { width: 12px; height: 12px; margin: 0 6px; } }
@media screen and (max-width: 991px) { .slick-dots li { width: 10px; height: 10px; margin: 0 4px; } }
@media screen and (max-width: 767px) { .slick-dots li { width: 8px; height: 8px; margin: 0 2px; } }
.slick-prev, .slick-next { background-color: transparent; border: 0; outline: none; cursor: pointer; position: absolute; z-index: 9; top: 50%; -webkit-transform: translateY(-50%); -moz-transform: translateY(-50%); -ms-transform: translateY(-50%); -o-transform: translateY(-50%); transform: translateY(-50%); font-size: 17px; font-size: 1.7rem; border-radius: 50%; width: 60px; height: 60px; line-height: 60px; text-align: center; background-color: #F5F5F5; background-color: rgba(245, 245, 245, 0.2); color: #fff; display: none!important;}
.slick-prev:hover, .slick-next:hover { background-color:  #fff; color: var(--common-body-font-emphasisColor); }
@media screen and (max-width: 1459px) { .slick-prev, .slick-next { width: 40px; height: 40px; line-height: 40px; } }
@media screen and (max-width: 1259px) { .slick-prev, .slick-next { width: 34px; height: 34px; line-height: 34px; } }
@media screen and (max-width: 991px) { .slick-prev, .slick-next { font-size: 16px; font-size: 1.6rem; width: 30px; height: 30px; line-height: 30px; } }
@media screen and (max-width: 767px) { .slick-prev, .slick-next { font-size: 16px; font-size: 1.6rem; width: 28px; height: 28px; line-height: 28px; } }
.slick-prev { left: 5.833333333333333%; }
@media screen and (max-width: 991px) { .slick-prev { display: none !important; } }
@media screen and (max-width: 767px) { .slick-prev { display: none !important; } }
.slick-next { right: 5.833333333333333%; }
@media screen and (max-width: 991px) { .slick-next { display: none !important; } }
@media screen and (max-width: 767px) { .slick-next { display: none !important; } }
.slideshow .item.slick-active.item_one img { -webkit-transform: scale(1.1); -moz-transform: scale(1.1); -ms-transform: scale(1.1); -o-transform: scale(1.1); transform: scale(1.1); }
.slideshow .item.slick-active.item_one.on img { -webkit-transform: scale(1); -moz-transform: scale(1); -ms-transform: scale(1); -o-transform: scale(1); transform: scale(1); }
.slick-list, .slick-slider, .slick-track { position: relative; display: block; }
.slick-loading .slick-slide, .slick-loading .slick-track { visibility: hidden; }
.slick-slider { -moz-box-sizing: border-box; box-sizing: border-box; -webkit-user-select: none; -moz-user-select: none; -ms-user-select: none; user-select: none; -webkit-touch-callout: none; -khtml-user-select: none; -ms-touch-action: pan-y; touch-action: pan-y; -webkit-tap-highlight-color: transparent; }
.slick-list { overflow: hidden; margin: 0; padding: 0; }
.slick-list:focus { outline: 0; }
.slick-list.dragging { cursor: pointer; cursor: hand; }
.slick-slider .slick-list, .slick-slider .slick-track { -webkit-transform: translate3d(0, 0, 0); -moz-transform: translate3d(0, 0, 0); -ms-transform: translate3d(0, 0, 0); -o-transform: translate3d(0, 0, 0); transform: translate3d(0, 0, 0); }
.slick-track { top: 0; left: 0; }
.slick-track:after, .slick-track:before { display: table; content: ''; }
.slick-track:after { clear: both; }
.slick-slide { display: none; float: left; height: 100%; min-height: 1px; }
[dir=rtl] .slick-slide { float: right; }
.slick-slide img { display: block; }
.slick-slide.slick-loading img { display: none; }
.slick-slide.dragging img { pointer-events: none; }
.slick-initialized .slick-slide { display: block; }
.slick-vertical .slick-slide { display: block; height: auto; border: 1px solid transparent; }
.slick-arrow.slick-hidden { display: none; }
.slick-slide iframe { position: absolute; left: 0; top: 0; width: 100%; height: 100%; }
.slick-slide video { position: absolute; display: block; left: 50%; top: 50%; -webkit-transform: translate(-50%, -50%); -moz-transform: translate(-50%, -50%); -ms-transform: translate(-50%, -50%); -o-transform: translate(-50%, -50%); transform: translate(-50%, -50%); min-width: 101%; min-height: 100%; max-width: none; -o-object-fit: cover; object-fit: cover; -o-object-position: 50% 50%; object-position: 50% 50%; display: none \9; display: block \9\0; }
.slick-dots .slide-count { display: none; }
@media screen and (max-width: 991px) { .slick-slide video { -o-object-position: inherit; object-position: inherit; -o-object-fit: inherit; object-fit: inherit; width: 100%; height: 100%; -webkit-transform: translate(0, 0); -moz-transform: translate(0, 0); -ms-transform: translate(0, 0); -o-transform: translate(0, 0); transform: translate(0, 0); left: 0; top: 0; } }

@font-face {
  font-family: "iconfont"; /* Project id 2906383 */
  src: url('../fonts/iconfont.woff2?t=1635748323857') format('woff2'),
       url('../fonts/iconfont.woff?t=1635748323857') format('woff'),
       url('../fonts/iconfont.ttf?t=1635748323857') format('truetype');
}

.iconfont {font-family: "iconfont" !important;font-style: normal;-webkit-font-smoothing: antialiased;-moz-osx-font-smoothing: grayscale;}
.icon-fasong:before {content: "\e642";}.icon-up9:before {content: "\e72a";}.icon-bottom9:before {content: "\e72b";}.icon-up7:before {content: "\e722";}.icon-bottom7:before {content: "\e723";}.icon-left7:before {content: "\e724";}.icon-right7:before {content: "\e725";}.icon-up16:before {content: "\e746";}.icon-bottom16:before {content: "\e747";}.icon-left16:before {content: "\e748";}.icon-right16:before {content: "\e749";}.icon-liebiao1:before {content: "\e834";}.icon-search:before {content: "\e616";}.icon-top10:before {content: "\e7ee";}.icon-bottom10:before {content: "\e7ef";}.icon-left10:before {content: "\e7f0";}.icon-right10:before {content: "\e7f1";}.icon-list:before {content: "\e671";}.icon-sousuo:before {content: "\e60d";}.icon-liebiao:before {content: "\e755";}.icon-wrong1:before {content: "\e633";}.icon-success1:before {content: "\e89f";}.icon-dianzan:before {content: "\e60c";}.icon-dianzan1:before {content: "\e63d";}.icon-aliwangwang-aliwangwang:before {content: "\e60a";}.icon-wenhao--jishu:before {content: "\e613";}
.icon-wenhao1--jishu:before {content: "\e69b";}.icon-home:before {content: "\e624";}.icon-edit:before {content: "\e611";}.icon-facebook:before {content: "\e620";}.icon-pinterest:before {content: "\e8ab";}.icon-linkedin:before {content: "\e648";}.icon-youtube:before {content: "\e6d6";}.icon-twitter:before {content: "\e607";}.icon-bigger:before {content: "\e647";}.icon-left:before {content: "\e72c";}.icon-right:before {content: "\e72d";}.icon-up1:before {content: "\e72e";}.icon-bottom1:before {content: "\e72f";}.icon-left1:before {content: "\e730";}.icon-right1:before {content: "\e731";}.icon-up:before {content: "\e73e";}.icon-bottom:before {content: "\e73f";}.icon-instagram:before {content: "\e79d";}.icon-tumblr:before {content: "\e79e";}.icon-download:before {content: "\e7a0";}.icon-download1:before {content: "\e7a7";}.icon-search4:before {content: "\e7b2";}.icon-address:before {content: "\e7bc";}.icon-email:before {content: "\e7bd";}.icon-contact:before {content: "\e7be";}.icon-whatsapp:before {content: "\e7bf";}.icon-fax:before {content: "\e7c2";}.icon-mobile:before {content: "\e7c6";}.icon-phone:before {content: "\e7c8";}.icon-tel:before {content: "\e7c7";}.icon-phone1:before {content: "\e7cb";}.icon-skype:before {content: "\e7cc";}.icon-whatsapp1:before {content: "\e7c1";}.icon-address1:before {content: "\e7c3";}.icon-contact1:before {content: "\e7c4";}.icon-email1:before {content: "\e7c5";}.icon-skype1:before {content: "\e7c9";}.icon-fax1:before {content: "\e7ca";}.icon-qq:before {content: "\e7ce";}.icon-tel1:before {content: "\e7cd";}.icon-wechat:before {content: "\e7cf";}.icon-phone2:before {content: "\e7d0";}.icon-message:before {content: "\e7da";}.icon-calendar:before {content: "\e7e1";}.icon-calendar1:before {content: "\e7e3";}.icon-top:before {content: "\e7ed";}.icon-youbian:before {content: "\e60f";}.icon-shopcart:before {content: "\e635";}.icon-shopcart1:before {content: "\e636";}.icon-shopcart2:before {content: "\e637";}.icon-shopcart3:before {content: "\e638";}.icon-shopcart4:before {content: "\e639";}.icon-shopcart5:before {content: "\e63a";}.icon-collection:before {content: "\e7aa";}.icon-collection1:before {content: "\e7ab";}.icon-collection2:before {content: "\e7ac";}.icon-collection3:before {content: "\e7ad";}.icon-collection4:before {content: "\e7ae";}.icon-zuanshi:before {content: "\e675";}.icon-shuaxin:before {content: "\e614";}.icon-xing:before {content: "\e62d";}.icon-shuaxin1:before {content: "\e652";}.icon-hongxin:before {content: "\e63b";}.icon-APP:before {content: "\e6b0";}.icon-xing1:before {content: "\e610";}.icon-kefu1:before {content: "\e603";}.icon-xingzhuang:before {content: "\e70e";}.icon-APP1:before {content: "\e600";}.icon-chakan:before {content: "\e60b";}.icon-feedback2:before {content: "\e601";}.icon-right2:before {content: "\e602";}.icon-top2:before {content: "\e8ac";}.icon-left2:before {content: "\e8ad";}.icon-bottom2-copy:before {content: "\e8ae";}.icon-zhekoubeijing:before {content: "\e612";}.icon-zhekou:before {content: "\e656";}.icon-hot:before {content: "\e65c";}.icon-dianzanhongxin:before {content: "\e732";}.icon-youhuijuan:before {content: "\e615";}.icon-sort:before {content: "\e606";}.icon-sort1:before {content: "\e63c";}.icon-feedback:before {content: "\e69e";}.icon-vk:before {content: "\e735";}.icon-kefu:before {content: "\e628";}.icon-jian2:before {content: "\e605";}.icon-jia2:before {content: "\e608";}.icon-visible:before {content: "\e604";}.icon-hidden:before {content: "\e609";}.icon-riqi:before {content: "\e681";}
@use "sass:math";
@use "sass:math";
* { margin: 0; padding: 0; -moz-box-sizing: border-box; box-sizing: border-box; }
html { font-size: 62.5%; }
body { color: var(--common-body-font-color); font-family: var(--common-body-font-family); font-size: 14px; font-size: 1.4rem; max-width: 1920px; margin: 0 auto; overflow-x: hidden; }
@media screen and (max-device-width: 320px) { body { -webkit-text-size-adjust: 100%; } }
@media screen and (max-device-width: 480px) { body { -webkit-text-size-adjust: 100%; } }
@media only screen and (-webkit-min-device-pixel-ratio: 2) { body { -webkit-text-size-adjust: 100%; } }
@media only screen and (min-device-width: 768px) and (max-device-width: 1024px) { body { -webkit-text-size-adjust: 100%; } }
img { border: none; }
ul li { list-style-type: none; }
ul, form, p, a, img, table, tr, td, li, dd, dt, dl, span { margin: 0; padding: 0; list-style: none; }
a { text-decoration: none; color: #333; outline: none; -webkit-transition: 0.3s; -o-transition: 0.3s; -moz-transition: 0.3s; transition: 0.3s; }
h1, h2 { margin: 0; padding: 0; font-weight: normal; }
img { max-width: 100%; border: 0px solid #ccc; }
embed, video, iframe { max-width: 100%; }
input[type="submit"], textarea[type="submit"] { cursor: pointer; }
input[type="checkbox"], textarea[type="checkbox"] { cursor: pointer; }
input[type="radio"], textarea[type="radio"] { cursor: pointer; }
input, textarea, select { font-size: 14px; font-size: 1.4rem; border: none; outline: none; background: none; }
input:focus, textarea:focus, select:focus { outline: none; }
textarea { resize: none; }
.pull-left { float: left; }
.pull-right { float: right; }
.clear { clear: both; }
.fix { *zoom: 1; }
.fix:after, .fix:before { display: block; content: "clear"; height: 0; clear: both; overflow: hidden; visibility: hidden; }
/* html5 */
article, aside, dialog, footer, header, section, footer, nav, figure, menu { display: block; }
::-webkit-input-placeholder { color: #666; opacity: 1; }
/* WebKit browsers */
:-o-placeholder { color: #666; opacity: 1; }
/* Mozilla Firefox 4 to 18 */
::-moz-placeholder { color: #666; opacity: 1; }
/* Mozilla Firefox 19+ */
:-ms-input-placeholder { color: #666; opacity: 1; }
/* Internet Explorer 10+ */
::-moz-selection { color: #fff; background: var(--common-body-font-emphasisColor); }
::selection { color: #fff; background: var(--common-body-font-emphasisColor); }
.self-ellipsis { display: block; overflow: hidden; white-space: nowrap; text-overflow: ellipsis; }
.self-ellipsis-a * {
	display: inline-block!important;
}
.self-ellipsis-2 { display: -webkit-box !important; overflow: hidden; white-space: normal !important; text-overflow: ellipsis; word-wrap: break-word; -webkit-line-clamp: 2; -webkit-box-orient: vertical; }
.self-ellipsis-3 { display: -webkit-box !important; overflow: hidden; white-space: normal !important; text-overflow: ellipsis; word-wrap: break-word; -webkit-line-clamp: 3; -webkit-box-orient: vertical; }
.self-ellipsis-4 { display: -webkit-box !important; overflow: hidden; white-space: normal !important; text-overflow: ellipsis; word-wrap: break-word; -webkit-line-clamp: 4; -webkit-box-orient: vertical; }
.self-ellipsis-5 { display: -webkit-box !important; overflow: hidden; white-space: normal !important; text-overflow: ellipsis; word-wrap: break-word; -webkit-line-clamp: 5; -webkit-box-orient: vertical; }
.relative { position: relative; }
.absolute { position: absolute; }
.common-vc { position: absolute; top: 50%; -webkit-transform: translateY(-50%); -moz-transform: translateY(-50%); -ms-transform: translateY(-50%); -o-transform: translateY(-50%); transform: translateY(-50%); }
.common-hc { position: absolute; left: 50%; -webkit-transform: translateX(-50%); -moz-transform: translateX(-50%); -ms-transform: translateX(-50%); -o-transform: translateX(-50%); transform: translateX(-50%); }
.common-center { position: absolute; top: 50%; left: 50%; -webkit-transform: translate(-50%, -50%); -moz-transform: translate(-50%, -50%); -ms-transform: translate(-50%, -50%); -o-transform: translate(-50%, -50%); transform: translate(-50%, -50%); }
.text-right { text-align: right; }
.text-left { text-align: left; }
.text-center { text-align: center; }
.nlazy { min-width: 20%; }
.scale-big img { -webkit-transition: all 350ms; -o-transition: all 350ms; -moz-transition: all 350ms; transition: all 350ms; }
.scale-big:hover img { -webkit-transform: scale(1.1); -moz-transform: scale(1.1); -ms-transform: scale(1.1); -o-transform: scale(1.1); transform: scale(1.1); }
.scale-small img { -webkit-transition: all 350ms; -o-transition: all 350ms; -moz-transition: all 350ms; transition: all 350ms; -webkit-transform: scale(1.1); -moz-transform: scale(1.1); -ms-transform: scale(1.1); -o-transform: scale(1.1); transform: scale(1.1); }
.scale-small:hover img { -webkit-transform: scale(1); -moz-transform: scale(1); -ms-transform: scale(1); -o-transform: scale(1); transform: scale(1); }
.layui-layer{max-width: 100%;}
[class*="-item"] .item-pic, [class*="-item"] .item-pics { position: relative; display: block; text-align: center; overflow: hidden; }
[class*="-item"] .item-pic img, [class*="-item"] .item-pics img { display: block !important; width: 100%; }
[class*="-item"] .item-title { overflow: hidden; color: #333; }
[class*="-item"] .item-title a { display: block; color: #333; }
[class*="-item"] .item-title a:hover { color:  var(--common-body-font-emphasisColor) !important; }
[class*="-item"] .item-subt { overflow: hidden; color: #333; }
[class*="-item"] .item-subt a { display: block; color: #333; }
[class*="-item"] .item-subt a:hover { color:  var(--common-body-font-emphasisColor) !important; }
[class*="-item"] .item-desc { overflow: hidden; color: #666; }
[class*="-item"] .item-desc a { display: block; color: #666; }
[class*="-item"] .item-desc a:hover { color:  var(--common-body-font-emphasisColor) !important; }
.at-resp-share-element .at4-share-count-container { text-decoration: none; float: right; padding-right: 15px; line-height: 25px !important; }
.at-resp-share-element .at-icon { width: 24px !important; height: 24px !important; }
.at-style-responsive .at-share-btn { padding: 0 !important; border-radius: 2px !important; }
.at-resp-share-element .at-share-btn .at-icon-wrapper { width: 24px !important; height: 24px !important; }
.at-resp-share-element .at-share-btn { margin-bottom: 0 !important; margin-right: 3px !important; }
.at-resp-share-element .at-icon { width: 24px !important; height: 24px !important; }
.at-style-responsive .at-share-btn { padding: 0 !important; border-radius: 2px !important; }
.at-resp-share-element .at-share-btn .at-icon-wrapper { width: 24px !important; height: 24px !important; }
.at-resp-share-element .at-share-btn { margin-bottom: 0 !important; margin-right: 3px !important; }
.item-table { display: table; table-layout: fixed; width: 100%; height: 100%; }
.item-cell { display: table-cell; width: 100%; height: 100%; vertical-align: middle; }
/* .an-btn1 start */
.an-btn1 { position: relative; z-index: 1; overflow: hidden; -webkit-transition: background 0.4s linear, color 0.3s linear; -o-transition: background 0.4s linear, color 0.3s linear; -moz-transition: background 0.4s linear, color 0.3s linear; transition: background 0.4s linear, color 0.3s linear; }
.an-btn1:before { position: absolute; top: 50%; left: 50%; width: 120%; height: 50%; opacity: 0; filter: alpha(opacity=0); -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=0)"; z-index: -1; content: ''; background:  var(--common-body-font-emphasisColor); -webkit-transition: all 0.4s linear 0s; -o-transition: all 0.4s linear 0s; -moz-transition: all 0.4s linear 0s; transition: all 0.4s linear 0s; -webkit-transform: translateX(-50%) translateY(-50%) rotate(45deg); -moz-transform: translateX(-50%) translateY(-50%) rotate(45deg); -ms-transform: translateX(-50%) translateY(-50%) rotate(45deg); -o-transform: translateX(-50%) translateY(-50%) rotate(45deg); transform: translateX(-50%) translateY(-50%) rotate(45deg); }
.an-btn1 .iconfont { -webkit-transition: all .5s; -o-transition: all .5s; -moz-transition: all .5s; transition: all .5s; }
.an-btn1:hover { border-color:  var(--common-body-font-emphasisColor) !important; background-color:  var(--common-body-font-emphasisColor) \9; color: #fff !important; }
.an-btn1:hover .iconfont { color: #fff !important; }
.an-btn1:hover:before { height: 500%; opacity: 1; filter: alpha(opacity=100); -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=100)"; }
/* .an-btn1 end */
/* .an-btn2 start */
.an-btn2 { position: relative; z-index: 1; overflow: hidden; -webkit-transition: background 0.4s linear, color 0.3s linear; -o-transition: background 0.4s linear, color 0.3s linear; -moz-transition: background 0.4s linear, color 0.3s linear; transition: background 0.4s linear, color 0.3s linear; }
.an-btn2:before { position: absolute; top: 50%; left: 50%; width: 120%; height: 50%; opacity: 0; filter: alpha(opacity=0); -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=0)"; z-index: -1; content: ''; background: #fff; -webkit-transition: all 0.4s linear 0s; -o-transition: all 0.4s linear 0s; -moz-transition: all 0.4s linear 0s; transition: all 0.4s linear 0s; -webkit-transform: translateX(-50%) translateY(-50%) rotate(45deg); -moz-transform: translateX(-50%) translateY(-50%) rotate(45deg); -ms-transform: translateX(-50%) translateY(-50%) rotate(45deg); -o-transform: translateX(-50%) translateY(-50%) rotate(45deg); transform: translateX(-50%) translateY(-50%) rotate(45deg); }
.an-btn2 .iconfont { -webkit-transition: all .5s; -o-transition: all .5s; -moz-transition: all .5s; transition: all .5s; }
.an-btn2:hover { border-color:  var(--common-body-font-emphasisColor) !important; background-color: #fff \9; color:  var(--common-body-font-emphasisColor) !important; }
.an-btn2:hover .iconfont { color:  var(--common-body-font-emphasisColor) !important; }
.an-btn2:hover:before { height: 500%; opacity: 1; filter: alpha(opacity=100); -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=100)"; }
/* .an-btn2 end */
@media screen and (min-width: 992px) { .an-bd1 .item-backdrop { position: absolute; left: 0px; top: 0px; bottom: 0; right: 0; -webkit-transition: all 900ms ease; -o-transition: all 900ms ease; -moz-transition: all 900ms ease; transition: all 900ms ease; -webkit-transition: -webkit-transform 0.4s ease; transition: -webkit-transform 0.4s ease; -o-transition: -o-transform 0.4s ease; -moz-transition: transform 0.4s ease, -moz-transform 0.4s ease; transition: transform 0.4s ease; transition: transform 0.4s ease, -webkit-transform 0.4s ease, -moz-transform 0.4s ease, -o-transform 0.4s ease; -webkit-transform: scale(0, 1); -moz-transform: scale(0, 1); -ms-transform: scale(0, 1); -o-transform: scale(0, 1); transform: scale(0, 1); -webkit-transform-origin: right center; -moz-transform-origin: right center; -ms-transform-origin: right center; -o-transform-origin: right center; transform-origin: right center; }
  .an-bd1 .item-backdrop:before { position: absolute; content: ''; left: 0px; top: 0px; right: 0px; bottom: 0px; opacity: 0.5; filter: alpha(opacity=50); -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=50)"; background-color:  var(--common-body-font-emphasisColor); } }
@media screen and (min-width: 992px) { .an-bd1:hover .item-backdrop { -webkit-transform: scale(1, 1); -moz-transform: scale(1, 1); -ms-transform: scale(1, 1); -o-transform: scale(1, 1); transform: scale(1, 1); -webkit-transform-origin: left center; -moz-transform-origin: left center; -ms-transform-origin: left center; -o-transform-origin: left center; transform-origin: left center; } }

/*-------------table start-------------------------------------------------------------------*/
.table table { width: 100%; border-collapse: collapse; border: 1px solid #ccc; }
.table table p { line-height: 22px !important; }
.table table p { min-height: 22px !important; }
.table table, .table table a { color: #000; }
.table table tr td { padding: 5px 3px; border: 1px solid #ccc; }
@media screen and (max-width: 1000px) { .table { width: 100%; overflow: scroll; }  }

/*-------------table end---------------------------------------------------------------------*/
/*-------------proslide_menubtn start--------------------------------------------------------*/
.proslide_menubtn { display: none; width: 23px; height: 18px; position: absolute; top: 12px; right: 10px; cursor: pointer; -webkit-transition: all 0.4s ease 0s; -o-transition: all 0.4s ease 0s; -moz-transition: all 0.4s ease 0s; transition: all 0.4s ease 0s; z-index: 9; }
.proslide_menubtn span { display: block; width: 100%; height: 3px; margin-bottom: 3px; background-color: #fff; }
@media screen and (max-width: 767px) { .proslide_menubtn { display: block; } }

/*-------------proslide_menubtn end----------------------------------------------------------*/
/*-------------page-start--------------------------------------------------------------------*/
.page { text-align: right; overflow: hidden; font-size: 0; letter-spacing: 0; position: relative; }
.page a { text-align: center; display: inline-block; vertical-align: top; color: #666; border: solid 1px #CCCCCC; min-width: 30px; padding: 0 2px; height: 30px; line-height: 28px; margin: 0 5px 10px; font-size: 12px; font-size: 1.2rem; }
.page a .iconfont { font-size: 14px; font-size: 1.4rem; }
.page a.prev { min-width: 30px; }
.page a.next { min-width: 30px; }
.page a:hover, .page a.page-active { border-color:  var(--common-body-font-emphasisColor); background-color:  var(--common-body-font-emphasisColor); color: #FFFFFF; }
@media screen and (max-width: 1259px) { .page a { margin: 0 2px 10px; } }
.page .box-shownum { font-size: 0; letter-spacing: 0; display: inline-block; margin-left: 20px; }
@media screen and (max-width: 1259px) { .page .box-shownum { margin-left: 10px; } }
.page .box-shownum .title, .page .box-shownum .select { display: inline-block; vertical-align: top; font-size: 14px; font-size: 1.4rem; color: #333; line-height: 30px; }
.page .box-shownum .title { margin-right: 5px; }
.page .box-shownum .select { width: 60px; height: 30px; color: #333; line-height: 28px; border: 1px solid #E5E5E5; -moz-box-sizing: border-box; box-sizing: border-box; background-color: transparent; }
/*-------------page-end--------------------------------------------------------------------*/
/*-------------pro-share start-------------------------------------------------------------*/
.pro-share { font-size: 0; letter-spacing: 0; }
.pro-share .share-title { margin-right: 8px; display: inline-block; *display: inline; *zoom: 1; line-height: 26px; font-size: 16px; font-size: 1.6rem; color: #888; }
@media only screen and (max-width: 767px) { .pro-share .share-title { font-size: 14px; font-size: 1.4rem; } }
.pro-share .share-pic { display: inline-block; *display: inline; *zoom: 1; vertical-align: top; height: 26px; }

/*-------------pro-share end---------------------------------------------------------------*/
/*-------------m-oths----------------------------------------------------------------------*/
.m-oths { font-size: 16px; font-size: 1.6rem; }
.m-oths a { color: #333; }
.m-oths a:hover { color:  var(--common-body-font-emphasisColor); }
.m-oths .oths-item { line-height: 30px; margin-bottom: 14px; }
.m-oths .item-btn { width: 86px; height: 30px; line-height: 28px; border: solid 1px #333; color: #333; display: inline-block; text-align: center; margin-right: 16px; float: left; }
.m-oths .item-info { overflow: hidden; color: #666; }
@media screen and (max-width: 991px) { .m-oths { padding-top: 30px; }
  .m-oths .item-btn { margin-right: 10px; } }
@media screen and (max-width: 767px) { .m-oths { padding-top: 20px; font-size: 14px; font-size: 1.4rem; }
  .m-oths .oths-item { margin-bottom: 10px; }
  .m-oths .item-btn { width: 66px; height: 26px; line-height: 24px; } }
/*-------------bread start-----------------------------------------------------------------*/
.bread {font-size: 16px; font-size: 1.6rem; line-height: 24px; padding-top: 20px; padding-bottom:10px; overflow: hidden; }
.bread, .bread a { color: #666; }
.bread a:hover, .bread .bread-active { color:  var(--common-body-font-emphasisColor); }
@media screen and (max-width: 1259px) { .bread { font-size: 16px; font-size: 1.6rem; padding-top: 14px; padding-bottom: 14px; } }
@media screen and (max-width: 991px) { .bread { font-size: 14px; font-size: 1.4rem; padding-top: 10px; padding-bottom: 10px; } }
@media screen and (max-width: 767px) { .bread { font-size: 12px; font-size: 1.2rem; line-height: 20px; padding-top: 8px; padding-bottom: 8px; } }
/*-------------bread end-------------------------------------------------------------------*/
.search-box ::-webkit-input-placeholder { color: #888; opacity: 1; }
/* WebKit browsers */
.search-box :-o-placeholder { color: #888; opacity: 1; }
/* Mozilla Firefox 4 to 18 */
.search-box ::-moz-placeholder { color: #888; opacity: 1; }
/* Mozilla Firefox 19+ */
.search-box :-ms-input-placeholder { color: #888; opacity: 1; }
/* Internet Explorer 10+ */
.search { position: relative; width: 329px; }
@media screen and (max-width: 1259px) { .search { width: 230px; } }
@media screen and (max-width: 991px) {}
.search .search-box { overflow: hidden; width: 100%; height: 42px; cursor: pointer; border: solid 1px #CCCCCC; }
@media screen and (max-width: 1259px) { .search .search-box { height: 36px; } }
.search .search-box .iconfont { position: absolute; right: 0; top: 0; width: 52px; line-height: 42px; font-size: 16px; font-size: 1.6rem; background-color: #333; text-align: center; color: #fff; }
@media screen and (max-width: 1259px) { .search .search-box .iconfont { width: 36px; line-height: 36px; } }
.search .search-box .item-input { position: absolute; width: 100%; left: 0; top: 0; bottom: 0; line-height: 40px; padding: 0 55px 0 12px; color: #888; }
@media screen and (max-width: 1259px) { .search .search-box .item-input { line-height: 34px; } }
.search .search-box .item-submit { position: absolute; width: 52px; height: 100%; top: 0px; right: 0px; cursor: pointer; z-index: 1; }
@media screen and (max-width: 1259px) { .search .search-box .item-submit { width: 36px; } }

.container{position: relative;width: 100%;padding: 0 15px;margin: 0 auto;}
@media (min-width:768px){.container{width:750px;}}
@media (min-width:992px){.container{width:980px;}}
@media (min-width:1260px){.container{width:1230px;}}
@media (min-width:1520px){.container{width:1500px;}}
@media (min-width:1730px){.container{width:1700px;}}


.header .lang-box{line-height: 26px;font-size: 14px; font-size: 1.4rem;color: #666;}
.header .hb-box, .header .item-btns, .header .my-box{ font-size: 0; letter-spacing: 0;line-height: 27px; }
.header .hb-box > *, .header .item-btns > *, .header .my-box > * { display: inline-block; vertical-align: middle; font-size: 14px; font-size: 1.4rem;color: #666; }
.header .my-box { position: relative; }
.header .my-box .box-list { display: none; background-color: #fff; border: solid 1px #f0f0f0; position: absolute; top: 100%; z-index: 12; width: 160px; right: 0; }
.header .my-box .box-list a { display: block; padding: 5px; line-height: 20px; color: #666; font-size: 14px; font-size: 1.4rem; }
.header .my-box .box-list a:hover { background-color:  #F5F5F5; color: #333; }
.header .item-l {margin: 0 8px; }
@media screen and (max-width: 1259px) {
	.header .lang-box{line-height: 26px;font-size: 14px; font-size: 1.4rem;}
	.header .hb-box, .header .item-btns, .header .my-box{line-height: 26px; }
	.header .hb-box > *, .header .item-btns > *, .header .my-box > * { font-size: 14px; font-size: 1.4rem;}
	.header .item-l {margin: 0 8px; }
}
@media screen and (max-width: 991px) {
	.header .lang-box{line-height: 24px;font-size: 13px; font-size: 1.3rem;}
	.header .hb-box, .header .item-btns, .header .my-box{line-height: 24px; }
	.header .hb-box > *, .header .item-btns > *, .header .my-box > * { font-size: 13px; font-size: 1.3rem;}
	.header .item-l {margin: 0 6px; }
}
@media screen and (max-width: 767px) {
	.header .lang-box{line-height: 22px;font-size: 12px; font-size: 1.2rem;}
	.header .hb-box, .header .item-btns, .header .my-box{line-height: 22px; }
	.header .hb-box > *, .header .item-btns > *, .header .my-box > * { font-size: 12px; font-size: 1.2rem;}
	.header .item-l {margin: 0 6px; }
}



.nav-item{width: auto;position: relative;float: left;margin-right: 34px;padding: 20px 0;}

.sbnav-list{position: absolute;display: none;z-index: 10;}

.nav-item:hover .sbnav-list1{display: block;}
.sbnav-item1:hover .sbnav-list2{display: block;}
.sbnav-item2:hover .sbnav-list3{display: block;}


.m-hdheart{width: auto;position: relative;padding: 25px 0;}
.banner{position: relative;width: 100%;}

@media screen and (max-width: 1729px) {
	.nav-item{margin-right: 30px;padding: 20px 0;}
	.m-hdheart{padding: 23px 0;}
}

@media screen and (max-width: 1519px) {
	.nav-item{margin-right: 14px;padding: 18px 0;}
	.m-hdheart{padding: 22px 0;}
}
@media screen and (max-width: 1259px) {
	.header .item-btns{margin-right: 30px; }
	.nav-item{margin-right: 12px;padding: 16px 0;}
	.m-hdheart{padding: 20px 0;}
}
@media screen and (max-width: 991px) {.header .item-btns{margin-right: 30px; }}

.contain{position: relative;width: 100%;padding: 0 15px;margin: 0 auto;}
@media (min-width:768px){.contain{width:750px;}}
@media (min-width:992px){.contain{width:980px;}}
@media (min-width:1260px){.contain{width:1230px;}}
@media (min-width:1420px){.contain{width:1390px;}}
@media (min-width:1582px){.contain{width:1552px;}}
.containes{position: relative;width: 100%;padding: 0 15px;margin: 0 auto;}
@media (min-width:768px){.containes{width:750px;}}
@media (min-width:992px){.containes{width:980px;}}
@media (min-width:1260px){.containes{width:1230px;}}
@media (min-width:1500px){.containes{width:1472px;}}
.contain-wp{position: relative;width: 100%;padding: 0 15px;margin: 0 auto;}
@media (min-width:768px){.contain-wp{width:750px;}}
@media (min-width:992px){.contain-wp{width:980px;}}
@media (min-width:1260px){.contain-wp{width:1230px;}}


.pro-item{display: inline-block;vertical-align: top;}
.pro-item .item-pic { position: relative; background: #FFFFFF;}
.pro-item .item-pic img{width: 100%;max-width: 100%;}
.pro1-list .pro-item .item-pic{border: 1px solid #E8E8E8;}

.item-zhekou{position: absolute;right: 0;top: 0;line-height: 28px;font-size: 14px;background: var(--common-body-font-emphasisColor);border-radius: 14px;padding: 0 11px;color: #FFFFFF;-webkit-transition: all .5s; -o-transition: all .5s; -moz-transition: all .5s; transition: all .5s;}
.pro-item:hover .item-zhekou{visibility: hidden; opacity: 0; }
.pro1-list .pro-item .item-zhekou{right: 15px;top: 15px;}
.pro1-list .pro-item:hover .item-zhekou, .may-owl .pro-item:hover .item-zhekou {visibility: visible; opacity:1; }
.item-yushou{position: absolute;left: 0;top: 0;line-height: 28px;font-size: 14px;background: var(--common-body-font-emphasisColor);border-radius: 14px;padding: 0 11px;color: #FFFFFF;}
.pro1-list .pro-item .item-yushou{left: 15px;top: 15px;}

.add-cart{width: 100%;display: block;text-align: center;font-size: 16px;font-weight: 500;color: #FEFEFE;background: #000000;color: #fff;padding: 10px;line-height: 30px;}
.add-cart .iconfont{font-size: 25px;margin-right: 14px;}
.pro-item .yushou-time{min-height: 48px;width: 100%;text-align: center;display: flex;align-items: center;justify-content: center;font-size: 14px;font-family: Arial;line-height: 20px;color: #FFFFFF;background: var(--common-body-font-emphasisColor);position: absolute;right: 0;left: 0;bottom: 0;}
.pro-item .yushou-time .j-writetime{color: #FFFFFF;margin: 0;font-size: 14px;font-family: Arial;line-height: 20px;}
.pro-item .yushou1-time{bottom: auto;top:-38px; left:20px;right:20px; background: #FFFFFF;border: 1px solid #E8E8E8;color:#333;z-index:5;width:auto;}
.pro-item .yushou1-time .j-writetime{color: #333;}

.pro-item .item-backdrop { z-index: 9; overflow: hidden; position: absolute; right: 0;top: 0;display: block; -webkit-transition: all .5s; -o-transition: all .5s; -moz-transition: all .5s; transition: all .5s; visibility: hidden; opacity: 0; }
.pro1-list .pro-item .item-backdrop, .may-owl .pro-item .item-backdrop{right: 0;top: 50%;left: 0;transform: translateY(-50%);text-align: center;}
.pro-item:hover .item-backdrop { visibility: visible; opacity: 1; }
.pro-item .item-backdrop .item-linkbox { font-size: 0; letter-spacing: 0; -webkit-transition: all .5s; -o-transition: all .5s; -moz-transition: all .5s; transition: all .5s;text-align: center; }
.pro-item .item-backdrop .item-linkbox .item-link { display: block; margin-bottom:2px; }
.pro1-list .pro-item .item-backdrop .item-linkbox .item-link, .may-owl .pro-item .item-backdrop .item-linkbox .item-link { display: inline-block; margin:4px; }
.pro-item .item-backdrop .item-linkbox .iconfont { -webkit-transition: all .5s; -o-transition: all .5s; -moz-transition: all .5s; transition: all .5s; display: inline-block; width: 50px; height: 50px; background: #000000;text-align: center; line-height: 50px; color: #fff; font-size: 28px; font-size: 2.8rem;border-radius:0%;}
.pro1-list .pro-item .item-backdrop .item-linkbox .item-link .iconfont{color: #000;background: #E8E8E8;}
.pro-item .item-backdrop .item-linkbox .item-link .iconfont:hover { background-color: var(--common-body-font-emphasisColor); color: #fff; }
.pro-item .item-backdrop .item-linkbox .item-link.active .iconfont{background-color: var(--common-body-font-emphasisColor); color: #fff; }
.pro-item .item-body{padding-top: 20px;position: relative;text-align: left}
.pro1-list .pro-item .item-body{border: 1px solid #E8E8E8;padding: 10px 20px;}
.pro-item .item-body .item-title{font-size:18px;color: #010101;line-height: 24px;position: relative;overflow: unset;padding-right: 20px;}
.pro1-list .pro-item .item-body .fam{font-family: Arial;}
.pro-item .item-body .item-txt{font-size:16px;color: #777;line-height: 24px;position: relative;margin-top: 6px;margin-bottom: -5px;}
.pro1-list .pro-item .item-body .item-txt{font-size:14px;}
.pro-item .item-body .item-title .item-lv{position: absolute;top: 0px;right: 0;}
.pro-item .item-body .item-price{font-size: 18px;color: #333;line-height: 32px;margin-top: 20px;font-weight: bold;display: flex;align-item:center; justify-content: space-between;}

.pro-item .item-body .item-price p{display: inline-block;vertical-align: top;color: #EF0000;}
.pro-item .item-body .item-price del{font-weight: normal;color: #707070;margin-right: 14px;}
.may-owl .pro-item .item-body .item-price{text-align: center;justify-content: center;}
.may-owl .pro-item .item-body .item-price p{color: #333;}
.pro-item .j-writetime { margin-top: 10px; margin-bottom: 7px; line-height: 1.3; font-size: 18px; font-size: 1.8rem; color: #555;}
.pro-item .item-progress { font-size: 0; letter-spacing: 0; margin-bottom: 9px; }
.pro-item .item-progress > * { display: inline-block; vertical-align: middle; }
.pro-item .item-progress .progress-line { overflow: hidden; width: 64.26117%; margin-right: 10px; height: 5px; border-radius: 3px; background-color: #F0F0F0; position: relative; }
.pro-item .item-progress .progress-line span { position: absolute; left: 0; top: 0; bottom: 0; background-color: #000710; border-radius: 3px; }
.pro-item .item-progress .progress-title { font-size: 14px; font-size: 1.4rem; color: #555; line-height: 20px; }
.pro-item .item-rate{margin-top: -10px;margin-bottom: -10px;}
.pro-item .item-rate .layui-rate li i.layui-icon{color: #F6CB35;margin-right:2px;font-size: 14px;font-size: 1.4rem;}
.pro-item .item-rate .rate-txt{font-size: 14px;font-size: 1.4rem;color: #333;line-height: 24px;padding: 10px 0;display: inline-block;vertical-align: middle;margin-right: 5px;}
@media (min-width:1260px) and (max-width: 1459px) {
	.pro-item .item-body{padding-top: 16px;}
	.pro-item .item-backdrop .item-linkbox .iconfont {width: 42px; height: 42px;line-height: 42px; font-size: 24px; font-size: 2.4rem;}
	.pro-item .item-body .item-title{font-size: 17px;line-height: 24px;}
	.pro-item .item-body .item-txt{font-size:15px;line-height: 24px;}
	.pro-item .item-body .item-price{font-size: 18px;line-height: 30px;margin-top: 16px;}
	.pro-item .j-writetime { margin-bottom: 6px; font-size: 16px; font-size: 1.6rem;}
	.pro-item .item-progress {margin-bottom:8px; }
	.pro-item .item-progress .progress-title { font-size: 14px; font-size: 1.4rem; line-height: 20px; }
	.pro-item .item-rate .rate-txt{font-size: 14px;font-size: 1.4rem;line-height: 24px;padding: 10px 0;}
	.pro-item .yushou-time{min-height: 48px;font-size: 14px;line-height: 20px;}
	.pro1-list .pro-item .item-zhekou{right: 15px;top: 15px;}
	.pro1-list .pro-item .item-yushou{left: 15px;top: 15px;}
}
@media (min-width:992px) and (max-width: 1259px) {
	.pro-item .item-backdrop .item-linkbox .iconfont {width: 36px; height: 36px;line-height: 36px; font-size: 18px; font-size: 1.8rem;}
	.pro-item .item-body{padding-top: 14px;}
	.pro-item .item-body .item-title{font-size: 16px;line-height: 24px;}
	.pro-item .item-body .item-txt{font-size:14px;line-height: 22px;}
	.pro-item .item-body .item-price{font-size: 18px;line-height: 30px;margin-top: 16px;}
	.pro-item .j-writetime { margin-bottom: 6px; font-size: 14px; font-size: 1.4rem;}
	.pro-item .item-progress {margin-bottom:8px; }
	.pro-item .item-progress .progress-title { font-size: 14px; font-size: 1.4rem; line-height: 20px; }
	.pro-item .item-rate .rate-txt{font-size: 13px;font-size: 1.3rem;line-height: 24px;padding: 10px 0;}
	.pro-item .item-rate .layui-rate li i.layui-icon {margin-right:2px;font-size: 13px;font-size: 1.3rem;}
	.ps-tip{line-height: 24px;font-size: 12px;font-size: 1.2rem; padding: 0 6px;min-width: 40px;}
	.add-cart .item-link{font-size: 14px;line-height: 36px;}
	.pro-item .yushou-time{min-height: 42px;font-size: 13px;line-height: 20px;}
	.pro-item .yushou-time .j-writetime {font-size: 13px;line-height: 20px;}
	.pro1-list .pro-item .item-zhekou{right: 12px;top: 12px;}
	.pro1-list .pro-item .item-yushou{left: 12px;top: 12px;}
	.pro1-list .pro-item .item-body{padding: 8px 16px;}
}
@media (max-width: 991px) {
	.pro-item .item-backdrop .item-linkbox .iconfont {width: 36px; height: 36px;line-height: 36px; font-size: 16px; font-size: 1.6rem;}
	.pro-item .item-body{padding-top: 13px;}
	.pro-item .item-body .item-title{font-size: 14px;line-height: 20px;}
	.pro-item .item-body .item-txt{font-size:12px;line-height: 20px;}
	.pro-item .item-body .item-price{font-size: 16px;line-height: 24px;}
	.pro-item .j-writetime { margin-bottom: 6px; font-size: 13px; font-size: 1.3rem;}
	.pro-item .item-progress {margin-bottom:8px; }
	.pro-item .item-progress .progress-title { font-size: 12px; font-size: 1.2rem; line-height: 20px; }
	.pro-item .item-rate .rate-txt{font-size: 12px;font-size: 1.2rem;line-height: 20px;padding: 8px 0;}

	.pro-item .item-rate .layui-rate li i.layui-icon {margin-right:2px;font-size: 12px;font-size: 1.2rem;}
	.ps-tip{line-height: 22px;font-size: 12px;font-size: 1.2rem; padding: 0 4px;min-width: auto;}
	.add-cart .item-link{font-size: 12px;line-height: 30px;}
	.pro-item .yushou-time{min-height: 36px;font-size: 13px;line-height: 18px;}
	.pro-item .yushou-time .j-writetime {font-size: 13px;line-height: 18px;}
	.pro1-list .pro-item .item-zhekou{right:9px;top: 9px;}
	.pro1-list .pro-item .item-yushou{left: 9px;top: 9px;}
	.pro1-list .pro-item .item-body{padding: 6px 12px;}
}
@media (max-width: 560px) {
	.pro-item .item-backdrop .item-linkbox .iconfont {width: 36px; height: 36px;line-height: 36px; font-size: 14px; font-size: 1.4rem;}
	.pro-item .item-body{padding-top: 12px;}
	.pro-item .item-body .item-title{font-size: 14px;line-height: 20px;}
	.pro-item .item-body .item-price{font-size: 15px;line-height: 24px;}
	.item-zhekou{font-size: 12px;line-height: 24px;}
	.item-yushou{font-size: 12px;line-height: 24px;}
	.add-cart{font-size: 12px;padding: 5px;line-height: 30px;}
	.add-cart .iconfont{font-size: 18px;margin-right: 10px;}
	.pro-item .item-rate .rate-txt{font-size: 12px;font-size: 1.2rem;line-height: 20px;padding: 10px 0;}
	.pro-item .item-rate .layui-rate li i.layui-icon {margin-right:0px;font-size: 10px;font-size: 1rem;}
	.pro-item .yushou-time{min-height: 36px;font-size: 12px;line-height: 18px;}
	.pro-item .yushou-time .j-writetime {font-size: 12px;line-height: 18px;}
	.pro1-list .pro-item .item-zhekou{right:6px;top: 6px;}
	.pro1-list .pro-item .item-yushou{left: 6px;top: 6px;}
	.pro1-list .pro-item .item-body{padding: 6px;}
}

.section{position: relative;width: 100%;}

.index-letter { background:#E2DAD7; padding: 7.395833333333333% 0 8.0208333333333%;position: relative; }
.index-letter-title{text-align: center;color: #AD744D;}
.index-letter-title a{padding: 0;color: #AD744D;}.index-letter-title a::before{width: 0;height: 0;}
.index-letter .item-info{width: 100%;max-width: 1240px;text-align: center;margin: 44px auto 50px;font-size: 24px;color: #AD744D;line-height: 26px;}
@media (min-width:1420px) and (max-width: 1581px) {.index-letter .item-info{margin: 38px auto 46px;font-size: 22px;line-height: 24px;}}@media (min-width:1260px) and (max-width: 1419px) {.index-letter .item-info{margin: 32px auto 40px;font-size: 20px;line-height: 22px;}}@media (min-width:992px) and (max-width: 1259px) {.index-letter .item-info{margin: 28px auto 36px;font-size: 18px;line-height: 21px;}}@media (max-width: 991px) {.index-letter .item-info{margin: 20px auto 30px;font-size: 16px;line-height: 20px;}}@media (max-width: 560px) {.index-letter .item-info{margin: 16px auto 20px;font-size: 14px;line-height: 20px;}}
.index-letter .item-form { overflow: hidden; position: relative; width: 100%; max-width: 464px;text-align: left;margin: 18px auto 0;display: block;padding-right: 133px;}
.index-letter .item-form ::-webkit-input-placeholder { color: #8c8c8c; opacity: 1; }
.index-letter .item-form ::-o-placeholder { color: #8c8c8c; opacity: 1; }
.index-letter .item-form ::-moz-placeholder { color: #8c8c8c; opacity: 1; }
.index-letter .item-form :-ms-input-placeholder { color: #8c8c8c; opacity: 1; }

@media screen and (max-width: 767px) { .index-letter .item-form { margin: 16px auto 0;padding-right: 148px;} }
@media screen and (max-width: 480px) { .index-letter .item-form { margin: 12px auto 0;padding-right: 126px;}}
.index-letter .item-form .item-input { display: block; width: 100%; height: 56px; line-height: 54px; border-radius: 0; background: #FFFFFF;border: 1px solid #373737; padding: 0 14px; color: #666;font-size: 16px; font-size: 1.6rem; }
@media screen and (max-width: 1459px){ .index-letter .item-form .item-input { font-size: 16px; font-size: 1.6rem;height: 52px; line-height: 50px; } }
@media screen and (max-width: 1259px){.index-letter .item-form .item-input { font-size: 14px; font-size: 1.4rem; }}
@media screen and (max-width: 991px) { .index-letter .item-form .item-input { font-size: 14px; font-size: 1.4rem;height: 48px; line-height: 46px; } }
@media screen and (max-width: 767px) { .index-letter .item-form .item-input {font-size: 14px; font-size: 1.4rem;  } }
@media screen and (max-width: 480px) { .index-letter .item-form .item-input { padding: 0 10px; } }
.index-letter .item-form .item-submit { cursor: pointer; width: 133px; height: 56px; line-height:56px; border-radius: 0; background:#373737; border: 1px solid #373737; top: 0; right: 0; position: absolute; color: #fff;font-size: 16px; font-size: 1.6rem; -webkit-transition: all .35s; -o-transition: all .35s; -moz-transition: all .35s; transition: all .35s; }
.index-letter .item-form .item-submit:hover { background-color: var(--common-body-font-emphasisColor); }
@media screen and (max-width: 1459px){ .index-letter .item-form .item-submit { font-size: 16px; font-size: 1.6rem;height: 52px; line-height: 52px; } }
@media screen and (max-width: 1259px){.index-letter .item-form .item-submit { width: 160px;font-size: 16px; font-size: 1.6rem; }}
@media screen and (max-width: 991px) { .index-letter .item-form .item-submit { font-size: 14px; font-size: 1.4rem;height: 48px; line-height: 44px;} }
@media screen and (max-width: 767px) { .index-letter .item-form .item-submit { width: 140px;  line-height: 38px; font-size: 14px; font-size: 1.4rem; } }
@media screen and (max-width: 480px) { .index-letter .item-form .item-submit { width: 120px; } }
.index-letter .item-form .error { color: #FF0000; }
.index-letter-follow{text-align: center;margin-top: 40px;font-size: 0;letter-spacing: 0;}
.index-letter-follow a { display: inline-block; vertical-align: top; margin-right: 32px; margin-bottom: 10px; position: relative; -webkit-transition: all .35s; -o-transition: all .35s; -moz-transition: all .35s; transition: all .35s; }
.index-letter-follow a:last-child { margin-right: 0; }
.index-letter-follow a:hover { margin-top: -5px; margin-bottom: 5px; }
.index-letter-follow .iconfont { font-size: 30px; font-size: 3rem; position: relative; display: block; color: #222; }
@media screen and (max-width: 1659px) {.index-letter-follow{margin-top: 36px;} .index-letter-follow a { margin-right: 28px; } .index-letter-follow .iconfont { font-size: 28px; font-size: 2.8rem;}}
@media screen and (max-width: 1459px) { .index-letter-follow{margin-top: 32px;} .index-letter-follow a { margin-right: 24px; } .index-letter-follow .iconfont { font-size: 24px; font-size: 2.4rem; } }
@media screen and (max-width: 1259px) { .index-letter-follow{margin-top: 24px;} .index-letter-follow a { margin-right: 16px; } .index-letter-follow .iconfont  { font-size: 20px; font-size: 2rem; } }



.m-prob1 .prob-pic-small{padding: 0 44px;}
.m-prob1 .prob-pic-small .prob-pic-btn{width: 32px;background: #FFFFFF;border: 1px solid #DCDCDC;}
.m-prob1 .prob-pic-small .prob-pic-btn:hover{width: 32px;background: var(--common-body-font-emphasisColor);border: 1px solid var(--common-body-font-emphasisColor);color: #fff;}

.big-box-ljj.letter-dialog .m-letterDialog .slide{padding: 50px;}
.letter-img{width: 50%;height: 100%;float: left;position: relative;text-align: center;}
.big-box-ljj .slide .banner-img{max-width: 100%;max-height: 100%;height: 100%;width: calc();}
.big-box-ljj.letter-dialog .m-letterDialog .slide .letter-info{width: 50%;height: 100%;float: right;position: relative;padding-left: 28px;padding-top: 34px;}
.big-box-ljj.letter-dialog .m-letterDialog .slide .letter-info .title{font-size: 28px;font-weight: normal;color: #333333;line-height: 36px;text-align: left;padding: 0;margin-bottom: 14px;}
.big-box-ljj.letter-dialog .m-letterDialog .slide .letter-info .text{border-top: 1px solid #DCDCDC;border-bottom: 1px solid #DCDCDC;font-size: 16px;font-weight: normal;color: #666666;line-height: 26px;height: auto;-webkit-line-clamp: unset;text-align: left;padding: 32px 0;min-height: 198px;}
.big-box-ljj.letter-dialog .m-letterDialog .slide .letter-info .bot .title{font-size: 18px;font-weight: normal;color: #333333;line-height: 60px;}
.big-box-ljj.letter-dialog .letter-info .bot input {width: 85%;margin-right: 0%;}
.big-box-ljj.letter-dialog .letter-info .bot button {width: 15%;}
.big-box-ljj.letter-dialog .m-letterDialog .slide .letter-img .item-img{height: 100%;}
@media screen and (max-width: 1199px) {
	.big-box-ljj.letter-dialog .m-letterDialog .slide .letter-info{width: 50%;height: 100%;padding-left: 24px;padding-top: 28px;}
	.big-box-ljj.letter-dialog .m-letterDialog .slide .letter-info .title{font-size: 24px;line-height: 30px;margin-bottom: 12px;}
	.big-box-ljj.letter-dialog .m-letterDialog .slide .letter-info .text{font-size: 14px;line-height: 24px;padding: 26px 0;min-height: 140px;}
	.big-box-ljj.letter-dialog .m-letterDialog .slide .letter-info .bot .title {font-size: 16px;line-height: 40px;}
}
@media screen and (max-width: 991px) {
	.letter-img{width:100%;height: auto;position: relative;float: none;}
	.big-box-ljj.letter-dialog .m-letterDialog .slide .letter-info{width: 100%;height: auto;padding-left: 0;padding-top: 20px;padding-bottom: 40px;overflow: auto;float: none;max-height: 200px;overflow-y: auto}
	.big-box-ljj.letter-dialog .m-letterDialog .slide .letter-info .title{font-size: 18px;line-height: 24px;margin-bottom: 12px;}
	.big-box-ljj.letter-dialog .m-letterDialog .slide .letter-info .text{font-size: 14px;line-height: 24px;padding: 20px 0;min-height: auto;}
	.big-box-ljj.letter-dialog .m-letterDialog .slide .letter-info .bot .title {font-size: 16px;line-height: 28px;}
	/*.layui-layer{min-height: 768px;}*/
	.big-box-ljj .slide .banner-img{width: auto;height: auto;}
}
@media screen and (max-width: 460px) {
	.big-box-ljj.letter-dialog .m-letterDialog .slide .letter-info .title{font-size: 18px;line-height: 24px;margin-bottom: 12px;}
	.big-box-ljj.letter-dialog .m-letterDialog .slide .letter-info .text{font-size: 12px;line-height: 20px;padding: 20px 0;min-height: auto;}
	.big-box-ljj.letter-dialog .m-letterDialog .slide .letter-info .bot .title {font-size: 13px;line-height: 24px;}
	/*.layui-layer{min-height: 768px;}*/
}

.pro-item .m-prolv{display: inline-block;vertical-align: top;margin-left: 4px;line-height: 32px;}
.m-prolv {position: relative;z-index: 9;text-align: left;}
.m-prolv .level-icon {position: relative;display: inline-block;*display: inline;*zoom: 1;vertical-align: top; font-size: 20px;color: var(--common-body-font-emphasisColor);}
.m-prolv .level-info {position: absolute;bottom: 100%;left: 0px;display: none;width: 190px;padding: 10px;background-color: #fff;box-shadow: 0 0 3px 1px rgba(0, 0, 0, 0.1);-moz-box-sizing: border-box;box-sizing: border-box; z-index: 999;max-height: 170px;overflow-y: auto;}
.m-prolv .level-item {position: relative;overflow: hidden;padding-left: 20px;line-height: 16px;font-size: 12px;font-size: 1.2rem;margin-top: 5px;}

.m-prolv .level-info::-webkit-scrollbar { width: 8px; background: white;}
.m-prolv .level-info::-webkit-scrollbar-corner, .m-prolv .level-info::-webkit-scrollbar-thumb, .m-prolv .level-info::-webkit-scrollbar-track { border-radius: 4px; }
.m-prolv .level-info::-webkit-scrollbar-corner,.m-prolv .level-info::-webkit-scrollbar-track {  background-color: #ddd; box-shadow: inset 0 0 1px #ddd; }
.m-prolv .level-info::-webkit-scrollbar-thumb {  background-color: #c1c1c1; }

.m-prolv .level-item img {position: absolute;left: 0;top: 0;width: 16px;height: 16px;}
.m-prolv .level-item:first-child {margin-top: 0;}
.m-prolv .level-off {float: right;margin-left: 10px;color: #E8A802;}
.m-prolv .level-text {overflow: hidden;display: block;word-break: break-all;}



.m-prolv .level-info {font-weight: 300; z-index: 999; display: none; background-color: #fff; color: #666; padding: 5px 10px; position: absolute; bottom: 80px; left: 0; width: 220px; font-size: 14px; font-size: 1.4rem; box-shadow: 0 0 3px 1px rgba(0, 0, 0, 0.1); }
.m-prolv .level-info * { display: inline-block; vertical-align: middle; line-height: 26px; }
.m-prolv .level-info img { margin-right: 10px; margin-top: 7px; }
.m-prolv .level-info span { color: #F3981C; margin-left: 20px; }
.m-prolv .level-info .level-text { display: inline-block; width: 80px; overflow: hidden; margin: 0; color: #999; max-height: 18px; overflow-y: hidden; display: inline-block; vertical-align: middle; line-height: 26px; max-height: unset; }
.m-prolv .level-info li { float: unset; width: 100%; }
.m-prolv .level-info {z-index: 999;display: none;background-color: #fff;color: #666;padding: 5px 10px;position: absolute;bottom:35px;right: 30px;width: 220px;box-shadow: 0 0 3px 1px rgba(0,0,0,0.1);}

@media screen and (max-width: 991px) { .pro-item .m-prolv{margin-left: 8px;line-height: 24px;} .m-prolv .level-icon { font-size: 16px; } }
@media screen and (max-width: 460px) {.m-prolv .level-info {right: -45px;width: 172px;padding: 8px;}}

.swiper-sec4 .swiper-slide .pro-item .m-prolv .level-info  { right: unset; left: -200px;}
@media screen and (max-width: 460px) {
	.swiper-sec4 .swiper-slide .pro-item .m-prolv .level-info { left: -150px;}
}
/*-------------about start-----------------------------------------------------------------*/

.about{position: relative;padding-top: 20px; padding-bottom: 80px; }
.about-top {width: 100%;position: relative; }
.about-tbody{float: left;width: 44.16666666667%;}
.about-tbody-img{width: 100%;position: relative;text-align: center;}
.about-tbody-info{font-size: 14px;color: #666666;line-height: 24px;font-family: Arial;padding-left: 30px;overflow: hidden;}
.about-tbody-info .box-title {font-size: 36px;text-align: left;color: #333333;line-height: 42px;margin-bottom: 10px; }
.about-form {padding-top: 60px;}
.about-form input, .about-form textarea, .about-form button, .about-form select { display: block; width: 100%; border: solid 1px #CCCCCC; background-color: #fff; border-radius: 4px; padding: 0 10px; font-size: 14px; font-size: 1.4rem; color: #666; }
.about-form input, .about-form button { height: 46px; line-height: 44px; }
.about-form textarea { height: 130px; padding-top: 8px; padding-bottom: 10px; }
.about-form .item-input, .about-form .item-input1 { margin-bottom: 29px; position: relative; }
.about-form .item-input { width: 46.875%; }
.about-form .item-input1 { width: 100%; }
.about-form .item-num { position: absolute; right: 5px; bottom: 25px; color: #999; }
.about-form .item-img { margin-left: 1.76056%; cursor: pointer; line-height: 46px; font-size: 0; margin-bottom: 29px;}
.about-form .item-img img { max-height: 46px; vertical-align: middle; }
.about-form .item-btn {width: 31.25%;}
.about-form .item-btn input, .about-form .item-btn button { cursor: pointer; background-color: var(--common-body-font-emphasisColor); font-size: 16px; font-size: 1.6rem; border: 0; color: var(--common-button-font-color); -webkit-transition: all .35s; -o-transition: all .35s; -moz-transition: all .35s; transition: all .35s; }
.about-form .item-btn input:hover, .about-form .item-btn button:hover { background-color: var(--common-body-font-emphasisColor); }
.about-box-pro{margin-top: 36px;}
.about-box-pro .pro-pic {width: 247px;border: 1px solid #ccc;line-height: 0;font-size: 0;letter-spacing: 0;margin-bottom: 8px;display: block;box-sizing: border-box;}
.about-box-pro .pro-title {font-size: 16px;font-size: 1.6rem;line-height: 24px;}
@media screen and (max-width: 1459px) {
	.about{ padding-top: 54px; padding-bottom: 80px; }
	.about-tbody{float: left;width: 44%;}
	.about-tbody-info{font-size: 14px;line-height: 20px;}
	.about-tbody-info .box-title {font-size: 32px;line-height: 36px;}
	.about-form {padding-top: 4px;}
	.about-form .box-title { font-size: 28px; font-size: 2.8rem; margin-bottom: 40px; }
	.about-form input, .about-form textarea, .about-form button, .about-form select { font-size: 14px; font-size: 1.4rem; }
	.about-form .item-input, .about-form .item-input1 { margin-bottom: 20px; }
	.about-form .item-btn {width: 31.25%;}
	.about-form .item-btn input, .about-form .item-btn button { font-size: 16px; font-size: 1.6rem; }
	.about-box-pro{margin-top: 36px;}
	.about-box-pro .pro-pic {width: 247px;}
	.about-box-pro .pro-title {font-size: 16px;font-size: 1.6rem;line-height: 24px;}
}
@media screen and (max-width: 1259px) {
	.about{ padding-top: 48px; padding-bottom: 70px; }
	.about-tbody{float: left;width: 44%;}
	.about-tbody-info{font-size: 14px;line-height: 20px;}
	.about-tbody-info .box-title {font-size: 30px;line-height: 32px;}
	.about-form {padding-top: 4px;}
	.about-form .box-title { font-size: 32px; font-size: 3.2rem; margin-bottom: 40px; }
	.about-form input, .about-form textarea, .about-form button, .about-form select { font-size: 14px; font-size: 1.4rem; }
	.about-form .item-input, .about-form .item-input1 { margin-bottom: 20px; }
	.about-form .item-code { width: 24.64789%; }
	.about-form .item-btn input, .about-form .item-btn button { font-size: 16px; font-size: 1.6rem; }
	.about-box-pro{margin-top: 32px;}
	.about-box-pro .pro-pic {width: 240px;}
	.about-box-pro .pro-title {font-size: 14px;font-size: 1.4rem;line-height: 24px;}
}
@media screen and (max-width: 991px) {
	.about{ padding-top: 26px; padding-bottom: 50px; }
	.about-tbody{float: none;width: 100%;}
	.about-tbody-info{font-size: 14px;line-height: 20px;margin-top: 20px;}
	.about-tbody-info .box-title {font-size: 26px;line-height: 30px;text-align: center;}
	.about-form {width: 100%;padding-top: 20px;}
	.about-form input, .about-form button { height: 42px; line-height: 40px; }
	.about-form .item-input, .about-form .item-input1 { margin-bottom: 15px; }
	.about-form .item-code { width: -webkit-calc(100% - 180px); width: -moz-calc(100% - 180px); width: calc(100% - 180px); }
	.about-form .item-img { float: right; margin-left: 0; line-height: 42px; }
	.about-form .item-img img { max-height: 42px; }
	.about-form .item-btn { width: 100%; }
	.about-box-pro{margin-top: 32px;}
	.about-box-pro .pro-pic {width: 220px;}
	.about-box-pro .pro-title {font-size: 14px;font-size: 1.4rem;line-height: 24px;}
}
@media screen and (max-width: 767px) {
	.about{ padding-top: 26px; padding-bottom: 50px; }
	.about-body{padding-bottom: 30px;}
	.about-body-left{float: none;width: 100%;margin-right: 0px;margin-bottom: 20px;}
	.about-body-title{padding-bottom: 16px;font-size: 20px;}
	.about-body-title::before{width: 64px;height: 4px;}
	.about-body-text{font-size: 14px;height: 130px;line-height: 26px;margin-top: 20px;}
	.about-body-href{margin-top: 20px;font-size: 16px;line-height: 30px;}
	.about-tbody{ font-size: 12px; font-size: 1.2rem; line-height: 20px;}
	.about-top {margin-bottom: 30px; }
	.about-top .box-title { font-size: 24px; font-size: 2.4rem; }
	.about-form { padding: 30px 0 20px; }
	.about-form .box-title { font-size: 18px; font-size: 1.8rem; margin-bottom: 20px; }
	.about-form input, .about-form textarea, .about-form button, .about-form select { font-size: 12px; font-size: 1.2rem; }
	.about-form .item-img { float: right; margin-left: 0; line-height: 36px; }
	.about-form .item-img img { max-height: 36px; }
	.about-form input, .about-form button { height: 36px; line-height: 34px; }
	.about-form .item-input, .about-form .item-input1 { margin-bottom: 10px; }
	.about-form .item-input { width: 100%; }
	.about-form .item-num { font-size: 12px; font-size: 1.2rem; }
	.about-form .item-code { width: -webkit-calc(100% - 150px); width: -moz-calc(100% - 150px); width: calc(100% - 150px); }
	.about-form .item-btn { width: 100%; }
	.about-form .item-btn input, .about-form .item-btn button { font-size: 14px; font-size: 1.4rem; }
}

.about-3v { margin-top: 66px; }
@media screen and (max-width: 1259px) { .about-3v { margin-top: 60px; } }
@media screen and (max-width: 991px) { .about-3v { margin-top: 45px; } }
@media screen and (max-width: 767px) { .about-3v { margin-top: 30px; } }
.about-3v .box-title { font-size: 36px; font-size: 3.6rem; line-height: 1.3; text-transform: uppercase; margin-bottom: 32px; }
@media screen and (max-width: 1259px) { .about-3v .box-title { font-size: 30px; font-size: 3rem; } }
@media screen and (max-width: 991px) { .about-3v .box-title { font-size: 26px; font-size: 2.6rem; margin-bottom: 30px; } }
@media screen and (max-width: 767px) { .about-3v .box-title { font-size: 20px; font-size: 2rem; margin-bottom: 20px; } }

/*-------------products-list start---------------------------------------------------------*/
.pro-main .box-main { width: 894px; }
.pro-main .box-slide { width: 284px; }
@media screen and (max-width: 1259px) { .pro-main .box-main { width: 690px; }.pro-main .box-slide { width: 240px; } }
@media screen and (max-width: 991px) { .pro-main .box-main { width: 100%; }.pro-main .box-slide { width: 100%; margin-top: 40px; } }
@media screen and (max-width: 767px) { .pro-main .box-slide { margin-top: 25px; } }

/* proslide start */
.proslide .yiji-list { margin-top: 9px; }
@media screen and (max-width: 767px) { .proslide .yiji-list { display: none; } }
.proslide .yiji-list > li > a { display: block; font-size: 16px; font-size: 1.6rem; line-height: 20px; padding: 7px 60px 7px 15px; position: relative; }
.proslide .yiji-list > li > a:before { position: absolute; content: ''; width: 6px; height: 6px; border-radius: 50%; background-color: #666; left: 0; top: 14px; }
.proslide .yiji-list > li > a span { width: 40px; position: absolute; right: 14px; top: 7px; font-size: 14px; font-size: 1.4rem; color: #ccc; }
.proslide .yiji-list > li > a .iconfont { font-size: 14px; font-size: 1.4rem; position: absolute; right: 0; top: 7px; color: #666; }
@media screen and (max-width: 1259px) { .proslide .yiji-list > li > a { font-size: 14px; font-size: 1.4rem; padding-top: 5px; padding-bottom: 5px; padding-right: 55px; }.proslide .yiji-list > li > a:before { top: 11px; }.proslide .yiji-list > li > a span { width: 40px; font-size: 12px; font-size: 1.2rem; top: 5px; }.proslide .yiji-list > li > a .iconfont { top: 5px; } }
.proslide .yiji-list > li:hover > a, .proslide .yiji-list > li.active > a { color:  var(--common-body-font-emphasisColor); }
.proslide .yiji-list > li:hover > a:before, .proslide .yiji-list > li.active > a:before { background-color:  var(--common-body-font-emphasisColor); }
.proslide .yiji-list > li:hover > a .iconfont:before, .proslide .yiji-list > li.active > a .iconfont:before { content: '\e723'; color:  var(--common-body-font-emphasisColor); }
.proslide .erji-list { background-color: #F6F6F6; padding: 10px 15px 20px; margin-top: 3px; }
@media screen and (max-width: 1259px) { .proslide .erji-list { padding: 10px; } }
.proslide .erji-list > li > a { display: block; font-size: 16px; font-size: 1.6rem; line-height: 20px; padding: 5px 40px 5px 0; color: #666; position: relative; }
.proslide .erji-list > li > a span { width: 40px; position: absolute; right: 0; top: 7px; font-size: 14px; font-size: 1.4rem; color: #ccc; }
@media screen and (max-width: 1259px) { .proslide .erji-list > li > a { font-size: 14px; font-size: 1.4rem; padding: 3px 0; }.proslide .erji-list > li > a span { width: 40px; font-size: 12px; font-size: 1.2rem; top: 3px; } }
.proslide .erji-list > li:hover > a, .proslide .erji-list > li.active > a { color:  var(--common-body-font-emphasisColor); }
.proslide .sanji-list > li > a { display: block; font-size: 16px; font-size: 1.6rem; line-height: 20px; padding: 5px 0 5px 22px; color: #666; }
@media screen and (max-width: 1259px) { .proslide .sanji-list > li > a { font-size: 14px; font-size: 1.4rem; padding: 3px 0 3px 15px; } }
.proslide .sanji-list > li:hover > a, .proslide .sanji-list > li.active > a { color:  var(--common-body-font-emphasisColor); }
/* proslide end */
/* like-box start */
.like-box { margin-top: 40px; }
@media screen and (max-width: 1259px) { .like-box { margin-top: 30px; } }
.like-box .slide-title { text-transform: capitalize; }
.like-box .box-list { font-size: 0; letter-spacing: 0; margin-top: 10px; }
.like-box .box-list .list-item { width: 100%; display: inline-block;vertical-align: top; margin-top: 19px;}
@media screen and (max-width: 1259px) { .like-box .box-list .list-item { margin-top: 15px; } }
.like-box .box-list .item-box { display: -webkit-box; display: -webkit-flex; display: -moz-box; display: -ms-flexbox; display: flex; -webkit-box-orient: horizontal; -webkit-box-direction: normal; -webkit-flex-flow: row nowrap; -moz-box-orient: horizontal; -moz-box-direction: normal; -ms-flex-flow: row nowrap; flex-flow: row nowrap; -webkit-box-pack: start; -webkit-justify-content: flex-start; -moz-box-pack: start; -ms-flex-pack: start; justify-content: flex-start; -webkit-box-align: center; -webkit-align-items: center; -moz-box-align: center; -ms-flex-align: center; align-items: center; }
.like-box .box-list .item-pic { -webkit-box-flex: 0; -webkit-flex: 0 0 auto; -moz-box-flex: 0; -ms-flex: 0 0 auto; flex: 0 0 auto; width: 82px; border: solid 1px #D9D9D9; border-radius: 4px; margin-right: 20px; }
@media screen and (max-width: 1259px) { .like-box .box-list .item-pic { margin-right: 10px; } }
.like-box .box-list .item-pic img { display: block; width: 100%; }
.like-box .box-list .item-body { -webkit-box-flex: 1; -webkit-flex: 1 1 auto; -moz-box-flex: 1; -ms-flex: 1 1 auto; flex: 1 1 auto; overflow: hidden; }
.like-box .box-list .item-title { font-size: 14px; font-size: 1.4rem; line-height: 1.3; margin-bottom: 11px; }
.like-box .box-list .item-price { line-height: 1.3; color:  var(--common-body-font-emphasisColor); font-size: 20px; font-size: 2rem; }
.like-box .box-list .item-price del { font-size: 12px; font-size: 1.2rem; color: #888; }
@media screen and (max-width: 1259px) { .like-box .box-list .item-price { font-size: 16px; font-size: 1.6rem; } }
/* like-box end */
.pro1-list { font-size: 0; letter-spacing: 0; padding-bottom: 57px;margin-left: -10px;margin-right: -10px; }
@media screen and (max-width: 1259px) { .pro1-list { padding-bottom: 30px;margin-left: -10px;margin-right: -10px; } }
@media screen and (max-width: 991px) { .pro1-list { padding-bottom: 20px;margin-left: -8px;margin-right: -8px; } }
@media screen and (max-width: 767px) { .pro1-list { padding-bottom: 10px; } }
.pro1-list .list-item { display: inline-block; vertical-align: top; width: 33.333333333333333%; padding: 0 10px; margin-bottom: 30px; position: relative; }
@media screen and (max-width: 1259px) { .pro1-list .list-item { padding: 0 10px;  } }
@media screen and (max-width: 991px) { .pro1-list .list-item { padding: 0 8px;  } }
@media screen and (max-width: 600px) { .pro1-list .list-item { width: 50%; }}



.ex-title { text-align: center; font-size: 30px; font-size: 3rem; line-height: 1.3; text-transform: uppercase; margin-bottom: 43px;margin-top: 30px; }
@media screen and (max-width: 1259px) { .ex-title { font-size: 26px; font-size: 2.6rem; margin-bottom: 40px;margin-top: 28px; } }
@media screen and (max-width: 991px) { .ex-title { font-size: 22px; font-size: 2.2rem; margin-bottom: 30px;margin-top: 25px; } }
@media screen and (max-width: 767px) { .ex-title { font-size: 18px; font-size: 1.8rem; margin-bottom: 20px;margin-top: 22px; } }
.group-ex {opacity: 0.8; background-color: #fff !important; }
.group-ex .item-title a:hover { color: #333 !important; }
.group-ex .item-backdrop, .group-ex .item-zhekou, .group-ex .item-yushou, .group-ex .yushou-time, .group-ex .ps-tip, .group-ex .add-cart { display: none !important; }
.group-ex .item-guoqi{position: absolute;left: 0;top: 0;width: 100%;height: 100%;overflow: hidden;}
.group-ex .item-guoqi span {position: absolute;left: 50%;top: 50%;-webkit-transform: translate(-50%, -50%);-moz-transform: translate(-50%, -50%);-ms-transform: translate(-50%, -50%);-o-transform: translate(-50%, -50%);transform: translate(-50%, -50%);width: 240px;max-width: 100%;padding: 10px;line-height: 36px;font-size: 24px;font-size: 2.4rem;color: #fff;text-align: center;background-color: rgba(0, 0, 0, 0.3);-moz-box-sizing: border-box;box-sizing: border-box;}
.group-ex .item-body {-webkit-filter: grayscale(100%);filter: grayscale(100%);filter: progid:DXImageTransform.Microsoft.BasicImage(grayscale=1); }

.pro-lists{font-size: 0;letter-spacing: 0;margin-left: -12px;margin-right: -12px;padding: 10px 0 40px;}
.pro-lists .pro-item{display: inline-block;vertical-align: top;width: 25%;padding: 0 12px;margin-bottom: 36px;}
@media screen and (max-width: 1459px) {.pro-lists{margin-left: -12px;margin-right: -12px;padding: 10px 0 30px;} .pro-lists .pro-item{width: 25%;padding: 0 12px;margin-bottom: 32px;} }
@media screen and (max-width: 1259px) { .pro-lists{margin-left: -10px;margin-right: -10px;padding: 10px 0 24px;} .pro-lists .pro-item{width: 25%;padding: 0 6px;margin-bottom: 28px;} .pro-lists .pro-item .item-rate .rate-txt{display:none}}
@media screen and (max-width: 991px) { .pro-lists{margin-left: -12px;margin-right: -12px;padding: 10px 0 24px;} .pro-lists .pro-item{width: 33.333333333%;padding: 0 6px;margin-bottom: 24px;} }
@media screen and (max-width: 767px) { .pro-lists{margin-left: -8px;margin-right: -8px;padding: 10px 0 20px;} .pro-lists .pro-item{width: 33.333333333%;padding: 0 4px;margin-bottom: 20px;}  }
@media screen and (max-width: 560px) { .pro-lists{margin-left: -8px;margin-right: -8px;padding: 10px 0 20px;} .pro-lists .pro-item{width: 50%;padding: 0 5px;margin-bottom: 20px;}  }
/*-------------products-list end-----------------------



/*-------------5-comment list start--------------------------------------------------------*/
/* .comment{background: rgba(235, 235, 235, .5);} */

/* comment-pro start */
.comment-pro {position: relative;width: 100%;}
.comment-pro { margin-bottom: 40px; }
.comment-pro .item-pic { width: 26.8333333333%;float: left; margin-right: 4.16667%; position: relative; }
.comment-pro .item-pic img { display: block; width: 100%; }
.comment-pro .item-body {overflow: hidden; }
.comment-pro .item-title { font-size: 20px; font-size: 2rem; line-height: 1.5; margin-top: 17px; margin-bottom: 30px; }
.comment-pro .item-desc { font-size: 16px; font-size: 1.6rem; line-height: 1.875; margin-bottom: 67px; }
.comment-pro .item-info { font-size: 0; letter-spacing: 0; position: relative; }
.comment-pro .item-info > * { display: inline-block; vertical-align: middle; }
.comment-pro .item-price { line-height: 1.3; font-size: 30px; font-size: 3rem; color: #EF0000; }
.comment-pro .item-price del { font-size: 16px; font-size: 1.6rem; color: #888; }
.comment-pro .item-lv { position: static; width: auto; margin-left: 5px; margin-top: 5px; }
.comment-pro .m-prolv .icon-info { min-width: 220px; max-width: 220px; margin-left: 5px; }

@media screen and (max-width: 1259px) {
	.comment-pro { margin-bottom: 30px; }
	.comment-pro .item-pic { margin-right: 2.5%; }
	.comment-pro .item-title { font-size: 18px; font-size: 1.8rem; margin-top: 15px; }
	.comment-pro .item-desc { font-size: 14px; font-size: 1.4rem; line-height: 1.6; margin-bottom: 50px; }
	.comment-pro .item-price { font-size: 26px; font-size: 2.6rem; }
	.comment-pro .item-price del { font-size: 14px; font-size: 1.4rem; }
}
@media screen and (max-width: 991px) {
	.comment-pro { margin-bottom: 20px; }
	.comment-pro .item-title { font-size: 16px; font-size: 1.6rem; margin-top: 10px; margin-bottom: 15px; }
	.comment-pro .item-desc { line-height: 1.4; margin-bottom: 20px; }
	.comment-pro .item-price { font-size: 22px; font-size: 2.2rem; }
}
@media screen and (max-width: 767px) {
	.comment-pro { margin-bottom: 10px; }
	.comment-pro .item-pic { width: 180px; }
	.comment-pro .item-title { font-size: 14px; font-size: 1.4rem; }
	.comment-pro .item-desc { font-size: 12px; font-size: 1.2rem; }
	.comment-pro .item-price { font-size: 18px; font-size: 1.8rem; }
	.comment-pro .item-price del { font-size: 12px; font-size: 1.2rem; }
}
@media screen and (max-width: 640px) {
	.comment-pro .item-pic { width: 150px; }
}
@media screen and (max-width: 480px) {
	.comment-pro .item-pic { float: none; margin: 0 auto; width: 200px; }
	.comment-pro .item-body { text-align: center; }
	.comment-pro .item-title { margin-bottom: 5px; }
	.comment-pro .item-desc { margin-bottom: 10px; }
	.comment-pro .m-prolv .icon-info { margin-left: 0; left: 50%; -webkit-transform: translateX(-50%); -moz-transform: translateX(-50%); -ms-transform: translateX(-50%); -o-transform: translateX(-50%); transform: translateX(-50%); }
}
/* comment-pro end */

.comment .f-box .box-title { font-size: 28px; font-size: 2.8rem; line-height: 1.3; text-transform: uppercase;margin-bottom: 16px;}
.comment-main {width: 100%;background: #fff;padding: 50px 10px;border-radius: 10px;}
.comment-main .cus-top { width: 100%;}
.comment-main .cus-top .left-item { float: left; width: 243px; padding: 13px 0 33px 0; margin-right: 4.08333%; }
.comment-main .cus-top .left-item .layui-rate-div { font-size: 0; letter-spacing: 0; margin-bottom: 27px; margin-top: 5px; }
.comment-main .cus-top .left-item .layui-rate-div > * { display: inline-block; vertical-align: middle; }
.comment-main .cus-top .left-item .layui-rate-div .layui-rate { padding: 0; }
.comment-main .cus-top .left-item .layui-rate-div .layui-rate li i.layui-icon { font-size: 15px; font-size: 1.5rem; margin-right: 1px; }
.comment-main .cus-top .left-item .layui-rate-div .rate-text { font-size: 12px; font-size: 1.2rem; color: #999; margin-left: 8px; }
.comment-main .cus-top .left-item .lines { border-right: solid 1px #DCDCDC; }
.comment-main .cus-top .left-item .lines .stars-item { font-size: 0; letter-spacing: 0; margin-bottom: 5px; }
.comment-main .cus-top .left-item .lines .stars-item > * { display: inline-block; vertical-align: middle; font-size: 14px; font-size: 1.4rem; line-height: 20px; color: #222; }
.comment-main .cus-top .left-item .lines .stars-item .star-text { width: 40px; }
.comment-main .cus-top .left-item .lines .stars-item .percent-line { width: 120px; height: 15px; background-color: #eee; margin: 0 11px 0 0; }
.comment-main .cus-top .left-item .lines .stars-item .percent-line span { display: block !important; width: 0%; height: 15px; background-color:  var(--common-body-font-emphasisColor); }
.comment-main .cus-top .right-item { overflow: hidden;}
.comment-main .cus-top .right-item .item-title { margin-top: 45px; margin-bottom: 6px; font-size: 0; letter-spacing: 0; }
.comment-main .cus-top .right-item .item-title > * { display: inline-block; vertical-align: middle; font-size: 16px; font-size: 1.6rem;}
.comment-main .cus-top .right-item .item-title > span:first-child { margin-right: 23px; }
.comment-main .cus-top .right-item .item-title .num { font-size: 40px; font-size: 4rem; color: #222; margin-right: 10px; }
.comment-main .cus-top .right-item .item-title .layui-rate-div { font-size: 0; letter-spacing: 0; }
.comment-main .cus-top .right-item .item-title .layui-rate-div > * { display: inline-block; vertical-align: middle; }
.comment-main .cus-top .right-item .item-title .layui-rate-div .layui-rate { padding: 0; }
.comment-main .cus-top .right-item .item-title .layui-rate-div .layui-rate li i.layui-icon { font-size: 15px; font-size: 1.5rem; margin-right: 1px; }
.comment-main .cus-top .right-item .item-title .layui-rate-div .rate-text { font-size: 14px; font-size: 1.4rem; color: #AAAAAA; margin-left: 12px; }
.comment-main .cus-top .right-item .item-subt { font-size: 14px; font-size: 1.4rem; line-height: 20px; color: #AAAAAA; margin-bottom: 18px; }
.comment-main .cus-top .right-item .item-btn { display: inline-block; line-height: 40px; padding: 0 33px; border-radius: 0; background-color:  var(--common-body-font-emphasisColor); color: #fff; font-size: 16px; font-size: 1.6rem; text-align: center; }
.comment-main .cus-top .right-item .item-btn:hover { background-color:  var(--common-body-font-emphasisColor); }
.clist-title { background-color: #F3F3F3; text-align: center; font-size: 30px; font-size: 3rem; line-height: 1.3; text-transform: uppercase; padding: 23px 0; margin-bottom: 72px; margin-top: 30px; }

@media screen and (max-width: 1259px) {

	.comment .f-box .box-title { font-size: 20px; font-size: 2rem; line-height: 1.2; padding-bottom: 6px;margin-bottom: 14px;}
	.comment-main .cus-top .right-item .item-title .num { font-size: 32px; font-size: 3.2rem; }
	.clist-title { margin-bottom: 60px; font-size: 26px; font-size: 2.6rem; margin-top: 0; }
}
@media screen and (max-width: 991px) {
	.comment .f-box .box-title { font-size: 18px; font-size: 1.8rem; line-height: 1.2; padding-bottom: 6px;margin-bottom: 12px;}
	.comment-main .cus-top .left-item { margin-right: 15px; width: 250px; }
	.comment-main .cus-top .right-item .item-title { margin-top: 50px; }
	.comment-main .cus-top .right-item .item-title .num { font-size: 28px; font-size: 2.8rem; margin-right: 8px; }
	.comment-main .cus-top .right-item .item-title .layui-rate-div .rate-text { margin-left: 10px; }
	.comment-main .cus-top .right-item .item-btn { line-height: 34px; padding: 0 25px; margin-bottom: 10px;}
	.clist-title { margin-bottom: 40px; font-size: 22px; font-size: 2.2rem; padding: 15px 0; }
}
@media screen and (max-width: 767px) {
	.comment-main .box-title { font-size: 20px; font-size: 2rem; }
	.comment-main .cus-top .left-item .layui-rate-div { margin: 0 0 10px; }
	.comment-main .cus-top .left-item { float: none; padding: 0; margin-bottom: 20px; }
	.comment-main .cus-top .left-item .lines { border: 0; }
	.comment-main .cus-top .right-item .item-title { margin-top: 0; }
	.comment-main .cus-top .right-item .item-title > * { font-size: 14px; font-size: 1.4rem; }
	.comment-main .cus-top .right-item .item-title .num { font-size: 24px; font-size: 2.4rem; }
	.comment-main .cus-top .right-item .item-title .layui-rate-div .rate-text { font-size: 12px; font-size: 1.2rem; margin-left: 0; }
	.comment-main .cus-top .right-item .item-subt { font-size: 12px; font-size: 1.2rem; margin-bottom: 10px; }
	.comment-main .cus-top .right-item .item-btn { font-size: 14px; font-size: 1.4rem; padding: 0 20px; line-height: 30px; margin-bottom: 20px;}
	.clist-title { margin-bottom: 20px; font-size: 18px; font-size: 1.8rem; padding: 10px 0; margin-top: 20px; }
}
@media screen and (max-width: 480px) {
	.comment-main .cus-top .right-item .item-title > span:first-child { display: block; margin-bottom: 5px; }
	.comment-main .cus-top .right-item .item-title .num { font-size: 20px; font-size: 2rem; }
}

/*-------------5-comment list end----------------------------------------------------------*/

/*-------------6-write comment start-------------------------------------------------------*/

.write-comment .box-title {font-size: 24px;font-size: 2.4rem;line-height: 36px;margin-bottom: 20px;color: #333; text-transform: uppercase;font-family: Arial;}
.write-comment .write-box{position: relative;}
.write-comment .write-box .left-item { float: left; width: 50.8333333%;}
.write-comment .write-box .left-item .item-pic { max-width: 610px; width: 100%; margin: 0 auto 28px; }
.write-comment .write-box .left-item .item-pic img { display: block; width: 100%; }
.write-comment .write-box .left-item .item-body{max-width: 500px; width: 100%;}
.write-comment .write-box .left-item .item-title { font-size: 16px; font-size: 1.6rem; line-height: 24px; margin-bottom: 17px; }
.write-comment .write-box .left-item .item-title, .write-comment .write-box .left-item .item-title a { color: #222; }
.write-comment .write-box .left-item .rate-div { font-size: 0; letter-spacing: 0; }
.write-comment .write-box .left-item .rate-div > * { display: inline-block; vertical-align: middle; }
.write-comment .write-box .left-item .rate-div .item-txt { font-size: 16px; font-size: 1.6rem; color: #999; line-height: 1.3; margin-right: 10px; }
.write-comment .write-box .left-item .rate-div .item-rate { font-size: 0; letter-spacing: 0; }
.write-comment .write-box .left-item .rate-div .item-rate > * { display: inline-block; vertical-align: middle; }
.write-comment .write-box .left-item .rate-div .layui-rate { padding: 0; }
.write-comment .write-box .left-item .rate-div .layui-rate li i.layui-icon { margin-right: 0; }
.write-comment .write-box .left-item .rate-div .rate-txt { font-size: 14px; font-size: 1.4rem; color: #999; margin-left: 5px;}


.write-comment .write-box .box-right {overflow: auto; padding: 36px 42px;position: absolute;right: 0;top:50px;background: #FFFFFF;box-shadow: 0px 3px 7px 0px rgba(0, 0, 0, 0.2);width:58.33333333333333%;}
.write-comment .write-box .box-right input, .write-comment .write-box .box-right textarea, .write-comment .write-box .box-right button { border: solid 1px #DCDCDC; border-radius: 5px; display: block; width: 100%; padding: 0 16px; font-size: 14px; font-size: 1.4rem; color: #AAAAAA; }
.write-comment .write-box .box-right input, .write-comment .write-box .box-right button { height: 46px; line-height: 44px; }
.write-comment .write-box .box-right textarea { height: 136px; padding: 5px 16px; }
.write-comment .write-box .box-right .item-row { margin-bottom: 18px; }
.write-comment .write-box .box-right .item-txt { font-size: 16px; font-size: 1.6rem; line-height: 20px; color: #222; margin-bottom: 5px; }
.write-comment .write-box .box-right .item-num { position: absolute; font-size: 12px; font-size: 1.2rem; line-height: 20px; color: #AAAAAA; right: 14px; bottom: 12px; }
.write-comment .write-box .box-right .item-img { float: left; cursor: pointer; line-height: 46px; font-size: 0; }
.write-comment .write-box .box-right .item-img img { max-height: 46px; vertical-align: middle; }
.write-comment .write-box .box-right .item-code { float: left; width: 190px; margin-right: 30px; }
.write-comment .write-box .box-right .item-submit { width: 156px; float: left;margin-left: 30px; }
.write-comment .write-box .box-right .item-submit input, .write-comment .write-box .box-right .item-submit button { cursor: pointer; background-color:  var(--common-body-font-emphasisColor); color: #fff; border: 0; padding: 0; font-size: 16px; font-size: 1.6rem; -webkit-transition: all .35s; -o-transition: all .35s; -moz-transition: all .35s; transition: all .35s; }
.write-comment .write-box .box-right .item-submit input:hover, .write-comment .write-box .box-right .item-submit button:hover { background-color: #000; }
.write-comment .write-box .box-right .layui-rate { padding: 0; }
.write-comment .write-box .box-right .layui-rate li i.layui-icon { font-size: 20px; font-size: 2rem; }
.write-comment .write-box .box-right .img-txt { font-size: 16px; font-size: 1.6rem; color: #222; line-height: 20px; margin-top: -4px; margin-bottom: 3px; }
.write-comment .write-box .box-right .img-tip { line-height: 20px; font-size: 14px; font-size: 1.4rem; color: #999; }
.write-comment .write-box .box-right .img-list { padding: 27px 0 6px; overflow: hidden; }
.write-comment .write-box .box-right .img-list li { width: 104px; margin-right: 13px; margin-bottom: 10px; float: left; }
.write-comment .write-box .box-right .img-list li .box { display: table-cell; vertical-align: middle; width: 104px; height: 104px; border: 2px dashed #D9D9D9; border-radius: 0; text-align: center; cursor: pointer; position: relative; }
.write-comment .write-box .box-right .img-list li .delete { display: none; position: absolute; top: -12px; right: -12px; }
.write-comment .write-box .box-right .img-list li .jia { vertical-align: middle; display: inline-block; width: 28px; height: 28px; background-image: url("../images/jia-big.png"); background-position: center; background-repeat: no-repeat; }
.write-comment .write-box .box-right .img-list li .num { position: absolute; bottom: 5px; left: 0; right: 0; margin: 0 auto; width: 25px; height: 10px; background-image: url("../images/img-num1.png"); background-position: center; background-repeat: no-repeat; }
.write-comment .write-box .box-right .img-list li:nth-child(2) .num { background-image: url("../images/img-num2.png"); }
.write-comment .write-box .box-right .img-list li:nth-child(3) .num { background-image: url("../images/img-num3.png"); }
.write-comment .write-box .box-right .img-list li .img { display: none; vertical-align: middle; }
.write-comment .write-box .box-right .img-list li.on .img { display: inline-block; }
.write-comment .write-box .box-right .img-list li.on .delete { display: block; }
.write-comment .write-box .box-right .img-list li.on .jia { display: none; }
.write-comment .write-box .box-right .img-list li.on .num { display: none; }
.write-comment .write-box .box-right .img-list li .file-input { top: 0; left: 0; position: absolute; width: 100%; height: 100%; filter: alpha(opacity=0); -moz-opacity: 0; -khtml-opacity: 0; opacity: 0; cursor: pointer; }

@media screen and (max-width: 1259px) {
	.write-comment .write-box{}
	.write-comment .write-box .left-item { width: 51%;}
	.write-comment .write-box .left-item .item-pic { margin-bottom: 20px; }
	.write-comment .write-box .left-item .item-title { margin-bottom: 10px; }
	.write-comment .write-box .box-right {padding: 24px 32px;top:20px;width: 58%;}
	.write-comment .write-box .box-right .item-row { margin-bottom: 15px; }
	.write-comment .write-box .box-right .item-code { width: 140px; margin-right: 20px; }
	.write-comment .write-box .box-right .item-submit { width: 140px; }
	.write-comment .write-box .box-right .layui-rate { margin-bottom: 0; }
	.write-comment .write-box .box-right .img-list { padding-top: 20px; }
	.write-comment .write-box .box-right .img-list li { width: 80px; }
	.write-comment .write-box .box-right .img-list li .box { width: 80px; height: 80px; }
}

@media screen and (max-width: 991px) {
	.write-comment .write-box .left-item { width: 100%; float: none; margin: 0 auto 30px; }
	.write-comment .write-box .left-item .item-pic { margin-bottom: 15px; }
	.write-comment .write-box .box-right {width: 100%;padding: 15px;position: relative;top:0;}
	.write-comment .write-box .box-right .item-code { width: 320px; margin-right: 10px; }
	.write-comment .write-box .box-right .img-list { padding-top: 15px; }
}

@media screen and (max-width: 767px) {
	.write-comment .box-title { font-size: 18px; font-size: 1.8rem; margin-bottom: 20px; }
	.write-comment .write-box .left-item .item-pic { margin-bottom: 10px; }
	.write-comment .write-box .left-item .item-title { margin-bottom: 5px;font-size: 14px;font-size: 1.4rem;line-height: 20px; }
	.write-comment .write-box .left-item .rate-div .item-txt { font-size: 14px; font-size: 1.4rem; margin-right: 5px; }
	.write-comment .write-box .left-item .rate-div .rate-txt { font-size: 12px; font-size: 1.2rem; margin-left: 3px; }
	.write-comment .write-box .box-right .item-txt { font-size: 14px; font-size: 1.4rem; }
	.write-comment .write-box .box-right .item-row { margin-bottom: 20px; }
	.write-comment .write-box .box-right .item-num { font-size: 12px; font-size: 1.2rem; right: 5px; bottom: 5px; }
	.write-comment .write-box .box-right .item-code { width: -webkit-calc(100% - 170px); width: -moz-calc(100% - 170px); width: calc(100% - 170px); }
	.write-comment .write-box .box-right .item-submit { margin-top: 10px; width: 100%; margin-left: 0;}
	.write-comment .write-box .box-right .item-row { margin-bottom: 15px; }
	.write-comment .write-box .box-right input, .write-comment .write-box .box-right textarea, .write-comment .write-box .box-right button { font-size: 12px; font-size: 1.2rem; padding-left: 10px; padding-right: 10px; }
	.write-comment .write-box .box-right input, .write-comment .write-box .box-right button { height: 32px !important; line-height: 30px !important; }
	.write-comment .write-box .box-right textarea { height: 100px; }
	.write-comment .write-box .box-right .item-img { line-height: 32px; }
	.write-comment .write-box .box-right .item-img img { max-height: 32px; vertical-align: middle; }
	.write-comment .write-box .box-right .item-code { width: -webkit-calc(100% - 120px); width: -moz-calc(100% - 120px); width: calc(100% - 120px); }
	.write-comment .write-box .box-right .item-submit { width: 100%; }
	.write-comment .write-box .box-right .item-submit input, .write-comment .write-box .box-right .item-submit button { font-size: 14px; font-size: 1.4rem; }
	.write-comment .write-box .box-right .img-txt { font-size: 14px; font-size: 1.4rem; line-height: 20px; }
	.write-comment .write-box .box-right .img-tip { font-size: 12px; font-size: 1.2rem; }
}

@media screen and (max-width: 640px) {
	.write-comment .write-box .box-right .img-list { padding: 10px 0 0; }
	.write-comment .write-box .box-right .img-list li { margin-right: 10px; }
	.write-comment .write-box .box-right .img-list li .delete { width: 18px; top: -9px; right: -9px; }
}

/*-------------6-write comment end---------------------------------------------------------*/
/* faq */
.faq-t { text-align: center;font-size: 42px;font-size: 4.2rem;line-height: 1.3;margin-bottom: 30px; text-transform: uppercase; color: var(--common-body-font-color); /*#333*/}
.faq-wrap { position: relative; min-height: 300px; }
.faq-slide { float: left; width: 280px; margin-bottom: 11px; z-index: 2; position: -webkit-sticky; position: sticky; left: 0; top: 0; }
.faq-slide .slide-yiji { border-top: solid 1px #DBDBDB; }
.faq-slide .slide-yiji > li { width: 100%; }
.faq-slide .slide-yiji > li > a { display: block; font-size: 16px; font-size: 1.6rem; color: var(--common-body-font-color); /*#333*/ line-height: 20px; padding: 12px 15px; border-bottom: solid 1px #DBDBDB; position: relative; }
.faq-slide .slide-yiji > li:hover > a, .faq-slide .slide-yiji > li.active > a { background-color:  var(--common-body-font-emphasisColor); color: #FFFFFF; }
.faq-title { font-size: 18px;font-size: 1.8rem;font-weight: bold;position: relative;border-bottom: solid 1px #ACACAC; padding-bottom: 15px; }
.faq-title:before { position: absolute; content: ''; width: 36px; height: 3px; bottom: -2px; left: 0; background-color:  var(--common-body-font-emphasisColor); }
.faq-title p, .faq-title a { display: block; line-height: 24px; position: relative; text-transform: uppercase; color: var(--common-body-font-color);}
.faq-title .iconfont { position: absolute; right: 0; top: 0; font-size: 24px; font-size: 2.4rem; cursor: pointer; }
.faq-title .iconfont:hover { color:  var(--common-body-font-emphasisColor); }
.faq-title a:hover { color:  var(--common-body-font-emphasisColor); }
.faq-title.faq-title2 { display: none; }
.faq-bottom { font-size:0;letter-spacing:0;margin-top: 30px;}
.faq-bottom >*{ display: inline-block;vertical-align: top;width: 50%;text-align:center}
.faq-bottom .faq-vr{ padding-top:25%;}
@media screen and (max-width: 1459px) {
	.faq-t { font-size: 36px; font-size: 3.6rem; margin-bottom: 24px; }
}
@media screen and (max-width: 1259px) {
	.faq-slide {width: 240px;}
	.faq-t { font-size: 30px; font-size: 3rem; margin-bottom: 20px; }
	.faq-slide .slide-yiji > li > a { font-size: 14px; font-size: 1.4rem; padding: 8px 10px; }
}
@media screen and (max-width: 991px) {
	.faq-t { font-size: 24px; font-size: 2.4rem; display: none; }
	.faq-slide { width: 100%; margin: 0 auto 30px; }
	.faq-slide .slide-yiji { display: none; }
	.faq-title { margin-bottom: 10px; padding-bottom: 10px; font-size: 18px; font-size: 1.8rem; }
	.faq-title.faq-title1 { display: none; }
	.faq-title.faq-title2 { display: block; }
}
@media screen and (max-width: 767px) {
	.faq-t { font-size: 20px; font-size: 2rem; }
	.faq-slide { position: relative; }
	.faq-bottom >*{width: 100%;}
	.faq-bottom .faq-vr{ padding-top:5%;}
}
.faq-main { float: right; width: 888px; }
.faq-list .box-title { background-color:  #f2f2f2; color: var(--common-body-font-color); /*#333*/ line-height: 30px; font-size: 16px; font-size: 1.6rem; padding: 10px; margin-bottom: 15px; }
.faq-list .list-item {background: #FFFFFF;border: 1px solid #E6E6E6; margin-bottom: 15px;border-radius: 15px;overflow: hidden; }
.faq-list .item-title { background-color: #fff; line-height: 24px; font-size: 14px; font-size: 1.4rem; padding: 8px 30px 8px 40px; cursor: pointer; position: relative; }
.faq-list .item-title span{position: absolute;left: 15px;top: 8px;}
.faq-list .item-title:before { position: absolute; content: '\e731'; font-family: "iconfont" !important; font-size: 14px; font-size: 1.4rem; display: block; font-style: normal; -webkit-font-smoothing: antialiased; -moz-osx-font-smoothing: grayscale; right: 8px; top: 8px; color: var(--common-body-font-color); /*#333*/ -webkit-transition: all .35s; -o-transition: all .35s; -moz-transition: all .35s; transition: all .35s; }
.faq-list .item-title.active { background-color: #fff; color:  var(--common-body-font-emphasisColor); }
.faq-list .item-title.active:before { -webkit-transform: rotate(90deg); -moz-transform: rotate(90deg); -ms-transform: rotate(90deg); -o-transform: rotate(90deg); transform: rotate(90deg); color: var(--common-body-font-emphasisColor); }
.faq-list .item-desc { display: none; padding: 10px; position: relative; font-size: 14px; font-size: 1.4rem; line-height: 24px;padding-left: 40px; }
.faq-list .item-desc span{position: absolute;left: 15px;top: 10px;}
.faq-list .item-desc p { min-height: 24px; }
.faq-list .item-desc .item-answer span { position: unset; }
.faq-page{margin-top: 50px;text-align: right;}
@media screen and (max-width: 1259px) {.faq-main { width: 670px; }.faq-page{margin-top: 42px;}}
@media screen and (max-width: 991px) {.faq-main { width: 100%; }.faq-page{margin-top: 34px;}}
@media screen and (max-width: 767px) {
	.faq-list .box-title { padding: 8px 10px; }
	.faq-list .item-title { line-height: 20px; border-left-width: 2px; }
	.faq-list .item-title:before { font-size: 12px; font-size: 1.2rem; }
	.faq-list .item-desc { border-left-width: 2px; font-size: 12px; font-size: 1.2rem; line-height: 20px; }
	.faq-list .item-desc p { min-height: 20px; }
	.faq-page{margin-top: 26px;}
}

/* seckill-bar start */
.seckill-bar { overflow: hidden; font-size: 0; letter-spacing: 0; text-align: right; }

.seckill-bar a { display: inline-block; vertical-align: top; font-size: 16px; font-size: 1.6rem; line-height: 24px; padding: 0 14px; position: relative; color: #666; }

.seckill-bar a:before { position: absolute; content: ''; left: 0; top: 4px; bottom: 4px; background-color: #999; width: 1px; }

.seckill-bar a:first-child { padding-left: 0; }

.seckill-bar a:last-child { padding-right: 0; }

.seckill-bar a:first-child:before { display: none; }

.seckill-bar a:hover, .seckill-bar a.active { color:  var(--common-body-font-emphasisColor); }

@media screen and (max-width: 991px) { .seckill-bar { float: left; width: 100%; text-align: left; }
  .seckill-bar a { padding: 0 10px; } }

@media screen and (max-width: 767px) { .seckill-bar a { font-size: 14px; font-size: 1.4rem; line-height: 20px; } }

/* seckill-bar end */
/* group-bar start */
.group-bar { overflow: hidden; font-size: 0; letter-spacing: 0; text-align: right; }

.group-bar a { display: inline-block; vertical-align: top; font-size: 16px; font-size: 1.6rem; line-height: 24px; padding: 0 14px; position: relative; color: #666; }

.group-bar a:before { position: absolute; content: ''; left: 0; top: 4px; bottom: 4px; background-color: #999; width: 1px; }

.group-bar a:first-child { padding-left: 0; }

.group-bar a:last-child { padding-right: 0; }

.group-bar a:first-child:before { display: none; }

.group-bar a:hover, .group-bar a.active { color:  var(--common-body-font-emphasisColor); }

@media screen and (max-width: 991px) { .group-bar { float: left; width: 100%; text-align: left; }
  .group-bar a { padding: 0 10px; } }

@media screen and (max-width: 767px) { .group-bar a { font-size: 14px; font-size: 1.4rem; line-height: 20px; } }

/* group-bar end */
.py-owl { overflow: hidden; }

.py-owl .list-item { margin: 0 3.27869%; background-color: #fff; }

.py-owl .item-videobox { width: 100%; overflow: hidden; margin: 0 auto; }

.py-owl .item-videobox .item-video { position: relative; overflow: hidden; padding-bottom: 71.2280701754386%; height: 0; }
.py-owl2 .item-videobox .item-video { position: relative; overflow: hidden; padding-bottom: 71.2280701754386%; height: 0; }
.py-owl .item-videobox .item-video iframe, .py-owl .item-videobox .item-video video, .py-owl .item-videobox .item-video img { position: absolute; top: 0; left: 0; width: 100%; height: 100%; border: none; }

.py-owl .item-videobox .item-video iframe body { margin: 0px !important; }

.py-owl .item-body { background-color: #F8F8F8; padding: 10px 15px 18px; }

@media screen and (max-width: 1259px) { .py-owl .item-body { padding: 10px; } }

@media screen and (max-width: 767px) { .py-owl .item-body { padding: 10px 5px; } }

.py-owl .item-title { font-size: 16px; font-size: 1.6rem; line-height: 24px; margin-bottom: 1px; }

@media screen and (max-width: 767px) { .py-owl .item-title { line-height: 20px; font-size: 14px; font-size: 1.4rem; } }

.py-owl .item-desc { font-size: 14px; font-size: 1.4rem; line-height: 26px; margin-bottom: 22px; }

@media screen and (max-width: 1259px) { .py-owl .item-desc { margin-bottom: 10px; line-height: 22px; } }

@media screen and (max-width: 767px) { .py-owl .item-desc { line-height: 20px; font-size: 12px; font-size: 1.2rem; } }

.py-owl .item-info { font-size: 0; letter-spacing: 0; display: flex;align-items: center;justify-content: space-between;flex-wrap: wrap;}

.py-owl .item-info .info-item { display: inline-block; vertical-align: top; color: #999; font-size: 12px; font-size: 1.2rem; line-height: 20px; margin-right: 15px; font-family: arial;}

.py-owl .item-info .info-item:last-child { margin-right: 0; }

.py-owl .item-info .iconfont { font-size: 12px; font-size: 1.2rem; line-height: 20px; }


.m-probadge .box-list { font-size: 0; letter-spacing: 0; }

.m-probadge .list-item { display: inline-block; vertical-align: top; margin-right: 10px; margin-bottom: 10px; }

.prodetails-tab .tab-options { background-color: #F3F3F3; margin-bottom: 75px; }
.prodetails-tab .tab-title{text-align: center;font-size: 34px;font-size: 3.4rem;color: var(--common-body-font-emphasisColor);line-height: 40px;padding: 40px 0;text-transform: uppercase;}

@media screen and (max-width: 1459px) { .prodetails-tab .tab-options { margin-bottom: 65px; } .prodetails-tab .tab-title{font-size: 30px;font-size: 3rem;line-height: 36px;padding: 32px 0;}}

@media screen and (max-width: 1259px) { .prodetails-tab .tab-options { margin-bottom: 65px; } .prodetails-tab .tab-title{font-size: 26px;font-size: 2.6rem;line-height: 32px;padding: 28px 0;}}

@media screen and (max-width: 991px) { .prodetails-tab .tab-options { margin-bottom: 45px; } .prodetails-tab .tab-title{font-size: 22px;font-size: 2.2rem;line-height: 28px;padding: 24px 0;}}

@media screen and (max-width: 767px) { .prodetails-tab .tab-options { margin-bottom: 25px; } .prodetails-tab .tab-title{font-size: 20px;font-size: 2rem;line-height: 28px;padding: 22px 0;}}

.prodetails-tab .tab-options .wp { display: -webkit-box; display: -webkit-flex; display: -moz-box; display: -ms-flexbox; display: flex; -webkit-box-orient: horizontal; -webkit-box-direction: normal; -webkit-flex-flow: row wrap; -moz-box-orient: horizontal; -moz-box-direction: normal; -ms-flex-flow: row wrap; flex-flow: row wrap; -webkit-box-pack: start; -webkit-justify-content: flex-start; -moz-box-pack: start; -ms-flex-pack: start; justify-content: flex-start; text-align: center; }

.prodetails-tab .tab-options .wp li { font-size: 24px; font-size: 2.4rem; line-height: 1.3; text-transform: uppercase; cursor: pointer; -webkit-box-flex: 1; -webkit-flex: 1; -moz-box-flex: 1; -ms-flex: 1; flex: 1; border-bottom: solid 3px transparent; display: -webkit-box; display: -webkit-flex; display: -moz-box; display: -ms-flexbox; display: flex; padding: 28px 5px 21px; }

.prodetails-tab .tab-options .wp li p { -webkit-align-self: center; -ms-flex-item-align: center; align-self: center; -webkit-box-flex: 1; -webkit-flex: 1; -moz-box-flex: 1; -ms-flex: 1; flex: 1; }

.prodetails-tab .tab-options .wp li:hover, .prodetails-tab .tab-options .wp li.tab-active { color:  var(--common-body-font-emphasisColor); border-bottom-color:  var(--common-body-font-emphasisColor); }

@media screen and (max-width: 1259px) { .prodetails-tab .tab-options .wp li { font-size: 20px; font-size: 2rem; padding: 20px 5px 17px; } }

@media screen and (max-width: 991px) { .prodetails-tab .tab-options .wp li { font-size: 16px; font-size: 1.6rem; padding: 10px 5px 4px; } }

@media screen and (max-width: 767px) { .prodetails-tab .tab-options .wp li { font-size: 14px; font-size: 1.4rem; } }

@media screen and (max-width: 480px) { .prodetails-tab .tab-options .wp li { -webkit-box-flex: 0; -webkit-flex: none; -moz-box-flex: 0; -ms-flex: none; flex: none; -webkit-flex-basis: 50%; -ms-flex-preferred-size: 50%; flex-basis: 50%; border-bottom-width: 2px; } }

.n-prot { font-size: 24px; font-size: 2.4rem; line-height: 1.3; text-align: center; text-transform: uppercase; margin-bottom: 42px; }

@media screen and (max-width: 1259px) { .n-prot { margin-bottom: 40px; font-size: 20px; font-size: 2rem; } }

@media screen and (max-width: 991px) { .n-prot { margin-bottom: 30px; font-size: 18px; font-size: 1.8rem; } }

@media screen and (max-width: 767px) { .n-prot { margin-bottom: 20px; font-size: 16px; font-size: 1.6rem; } }
.pro-3v-content{position: relative;font-size: 0;letter-spacing: 0;padding: 40px 0 20px;margin-left: -20px;margin-right: -20px;}

.pro-3v-item { display: inline-block;vertical-align: top;width: 50%;padding: 0 20px;margin-bottom: 25px; }
.pro-3v-item .tab-title{text-align: left;font-size: 34px;font-size: 3.4rem;color: #243912;line-height: 40px;padding: 40px 0;text-transform: uppercase;}
.pro-3v-item .item-videobox { width: 100%; overflow: hidden; margin: 0 auto; }

.pro-3v-item .item-videobox .item-video { position: relative; overflow: hidden; padding-bottom: 67.58620689655172%; height: 0; }
.pro-3v-item .item-videobox .item-video iframe, .pro-3v-item .item-videobox .item-video video, .pro-3v-item .item-videobox .item-video img { position: absolute; top: 0; left: 0; width: 100%; height: 100%; border: none; }

.pro-3v-item .item-videobox .item-video iframe body { margin: 0px !important; }

.pro-3v-item .item-body { background-color: #F8F8F8; padding: 10px 15px 18px; }

@media screen and (max-width: 1259px) { .pro-3v-item .item-body { padding: 10px; } }

@media screen and (max-width: 767px) { .pro-3v-item .item-body { padding: 10px 5px; } }

.pro-3v-item .item-title { font-size: 16px; font-size: 1.6rem; line-height: 24px; margin-bottom: 1px; }

@media screen and (max-width: 767px) { .pro-3v-item .item-title { line-height: 20px; font-size: 14px; font-size: 1.4rem; } }

.pro-3v-item .item-desc { font-size: 14px; font-size: 1.4rem; line-height: 26px; margin-bottom: 22px; }

@media screen and (max-width: 1259px) { .pro-3v-item .item-desc { margin-bottom: 10px; line-height: 22px; } }

@media screen and (max-width: 767px) { .pro-3v-item .item-desc { line-height: 20px; font-size: 12px; font-size: 1.2rem; } }

.pro-3v-item .item-info { font-size: 0; letter-spacing: 0; }

.pro-3v-item .item-info .info-item { display: inline-block; vertical-align: top; color: #999; font-size: 12px; font-size: 1.2rem; line-height: 20px; margin-right: 15px; }

.pro-3v-item .item-info .info-item:last-child { margin-right: 0; }

.pro-3v-item .item-info .iconfont { font-size: 12px; font-size: 1.2rem; }


@media screen and (max-width: 1459px) { .pro-3v-item .tab-title{font-size: 30px;font-size: 3rem;line-height: 36px;padding: 32px 0;}}

@media screen and (max-width: 1259px) { .pro-3v-item .tab-title{font-size: 26px;font-size: 2.6rem;line-height: 32px;padding: 28px 0;}.pro-3v-content{padding: 36px 0 16px;margin-left: -15px;margin-right: -15px;}
.pro-3v-item {width: 50%;padding: 0 15px;margin-bottom: 25px; }}

@media screen and (max-width: 991px) { .pro-3v-item .tab-title{font-size: 22px;font-size: 2.2rem;line-height: 28px;padding: 24px 0;}.pro-3v-content{padding: 30px 0 16px;margin-left: -10px;margin-right: -10px;}
.pro-3v-item {width: 50%;padding: 0 10px;margin-bottom: 20px; }}

@media screen and (max-width: 767px) { .pro-3v-item .tab-title{font-size: 20px;font-size: 2rem;line-height: 28px;padding: 22px 0;}.pro-3v-content{padding: 30px 0 12px;margin-left: -8px;margin-right: -8px;}
.pro-3v-item {width: 50%;padding: 0 8px;margin-bottom: 20px; }}
@media screen and (max-width: 420px) { .pro-3v-item {width: 100%;padding: 0 8px;margin-bottom: 20px; }}
.pro-3v{margin-top: 90px;}
@media screen and (max-width: 1259px) { .pro-3v { margin-top: 70px; } }

@media screen and (max-width: 991px) { .pro-3v { margin-top: 50px; } }

@media screen and (max-width: 767px) { .pro-3v { margin-top: 30px; } }


.pro-item .item-backdrop .item-linkbox .item-link.on .iconfont {
	color: var(--common-body-font-emphasisColor) !important;
}
.pro-item .item-backdrop .item-linkbox .item-link.on .iconfont:hover {
	background-color: #000 !important;
}
/*.m-prob1 .prob-info .btns img{position: relative;top: -3px}*/
#btn-notice{margin-left: 0!important;float: unset!important;}
@media (max-width:992px){#btn-notice{margin-left: 10px!important; }}

#aboutUsForm .about-form > div {
	min-height: 66px;
	margin-bottom: 9px;
}
#pictureViewer { width: 100%; height: 100%;position: fixed; top: 0; left: 0; background-color: rgba(0,0,0,1); z-index: 10000;}
#pictureViewer .content { width: 100%; height: 100%; display: flex; justify-content: center; align-items: center; position: relative;}
#pictureViewer .cover { max-width: 94%; max-height: 94%; position: relative; z-index: 1;}
.m-prob1 .prob-info .time.seckill-time {padding: 10px 0px 20px!important;}
/*.m-prob1 .prob-info .time.seckill-time .item-l{padding-left: 0!important;}*/
.big-box-ljj .details-content .promotion .list-item .icon{width: 23px}
.m-prob1 .prob-info .btns img{width: 23px}

@media screen and (max-width: 767px) {
	.fixed-box .bulletin-con .close{ right: 40px !important; }
}
.layui-layer-conform.layui-layer { max-width: 400px;}
.big-box-ljj .view-content .m-prob1 .prob-pic { position: sticky !important; top: 0;}

/*banner样式，首页其他图片样式*/
.index img{width: 100%;}
.dialog2 img{width: auto;}
.slick-list {width: 100%;height: 100% }
.slick-track {height: 100%}
.slick-slide img {width: 100%;height: 100%;object-fit: cover;}
/*banner样式，首页其他图片样式*/
.swiper-no-swiping .swiper-wrapper{ justify-content:center;}


/**
拼团促销
 */
.success .continue {
	background-color: var(--common-body-font-emphasisColor) !important;
}
/* 可视化 */
.tailorism_price_box .right .masonry_box { z-index: 10 !important;}
.product_page_second .product-pagination { text-align: center !important;}
@media screen and (min-width: 600px) { .producttype .vip-discount-box .discount-pop:nth-child(4n) { left: unset !important; right: -71px !important;} }

/*表格居中属性*/
table {margin-inline-start: auto;
	margin-inline-end: auto;
	border-top-width: 1px;
	border-bottom-width: 1px;
	border-left-width: 1px;
	border-right-width: 1px;
	-webkit-border-horizontal-spacing: 1px;
	-webkit-border-vertical-spacing: 1px;}
