function triggerButtonColor(element) {
  // var background = $(this).css('backgroundColor').replace(/\s+/g, '') === 'rgba(0,0,0,0)'
  $(element).on('mouseenter', function () {
      console.log($(this).get(0))
    var back = $(this).css('color');
    var hollow = $(this).hasClass('hollow')
    if (hollow) {
      var bg = $(this).css('border-color');
      //var color = dynamicColor(bg);
      //$(this).css({ background: bg, color })
      $(this).css({ background: bg})
    } else {
      // $(this).css({ background: rgbTorgba($(this).css('backgroundColor')), })
      document.body.style.setProperty('--solid-btn-hover-bgcolor', rgbTorgba($(this).css('backgroundColor')))
      $(this).addClass('solid-btn-hover')
    }
  })
  $(element).on('mouseleave', function () {
    var back = $(this).css('background-color')
    var hollow = $(this).hasClass('hollow')
    if (hollow) {
      //$(this).css({ background: 'transparent', color: rgbaTorgb(back) })
      $(this).css({ background: 'transparent'})
    } else {
      // $(this).css({ background: rgbaTorgb(back), })
      $(this).removeClass('solid-btn-hover')
    }
  })

    function rgbTorgba(rgb) {
        var rgba =rgb
        if(rgb.includes('rgba')){
            rgba = `${rgb.split(",").slice(0, rgb.split(",").length-1).toString()})`
           rgba = rgba.replace(/(\d+),\s*(\d+),\s*(\d+)/, "$1,$2,$3,0.5");
        }else{
             rgba = rgb.replace("rgb", "rgba");
             rgba = rgba.replace(/(\d+),\s*(\d+),\s*(\d+)/, "$1,$2,$3,0.5");
        }
        return rgba;
    }

    function rgbaTorgb(rgba) {
        var rgb = rgba.replace("rgba", "rgb");
        rgb = rgb.replace(/(\d+),\s*(\d+),\s*(\d+),\s*(\d+)/, "$1,$2,$3");
        return rgb
    }

    //根据深浅色背景，返回黑白文字颜色。
    function dynamicColor(bg) {
        if (!bg) {    //默认中色
            return '#999';
        }
        if (bg.search('gradient') != -1) {     //渐变色直接返回中色值
            return '#999';
        }
        if (bg.search('#') != -1) {     //16进制转换成rgb
            bg = set16ToRgb(bg);
        }
        if (bg.search('rgb') != -1) {     //rgb

        }
        var bgcolor = bg.replace("rgb(", "").replace("rgba(", "").replace(")", "");
        var bgcolorArry = bgcolor.split(",");
        return !isLight(bgcolorArry) ? '#ffffff' : '#000000';//浅色背景就返回深色文字颜色。
    }

    //是否浅色
    function isLight(rgb = [0, 0, 0]) {
        return (0.213 * rgb[0] + 0.715 * rgb[1] + 0.072 * rgb[2] > 255 / 2);
    };
    //【16进制转换为RGB 】
    // set16ToRgb('#ffffff');   // rgb(255,255,0)
    function set16ToRgb(str) {
        var reg = /^#([0-9A-Fa-f]{3}|[0-9A-Fa-f]{6})$/
        if (!reg.test(str)) {
            return;
        }
        let newStr = (str.toLowerCase()).replace(/\#/g, '')
        let len = newStr.length;
        if (len == 3) {
            let t = ''
            for (var i = 0; i < len; i++) {
                t += newStr.slice(i, i + 1).concat(newStr.slice(i, i + 1))
            }
            newStr = t
        }
        let arr = []; //将字符串分隔，两个两个的分隔
        for (var i = 0; i < 6; i = i + 2) {
            let s = newStr.slice(i, i + 2)
            arr.push(parseInt("0x" + s))
        }
        return 'rgb(' + arr.join(",") + ')';
    }

    //【RGB转换为16进制 】
    // setRgbTo16('rgb(255,0,255)');   // #FF00FF
    function setRgbTo16(str) {
        let reg = /^(rgb|RGB)/;
        if (!reg.test(str)) {
            return;
        }
        var arr = str.slice(4, str.length - 1).split(",")
        let color = '#';
        for (var i = 0; i < arr.length; i++) {
            var t = Number(arr[i]).toString(16)
            if (t == "0") {   //如果为“0”的话，需要补0操作,否则只有5位数
                t = t + "0"
            }
            color += t;
        }
        return color;
    }
}

// $(function() {
//   setTimeout(() => {
//     triggerButtonColor('.poster-minor')
//   }, 500)
// })
// 计算反色
function getReverseColor(color) {
    const hexToRGBA = (hexColor) => {
        if (hexColor[0] === '#') {
            hexColor = hexColor.slice(1);
        }
        if (hexColor.length !== 8) {
            return []
        }
        let r = parseInt(hexColor.slice(0, 2), 16);
        let g = parseInt(hexColor.slice(2, 4), 16);
        let b = parseInt(hexColor.slice(4, 6), 16);
        let a = parseInt(hexColor.slice(6, 8), 16) / 255;
        return [r, g, b, a];
    }
    const colorArr = color.match(/\(/) ? color.split('(')[1].split(')')[0].split(',') : hexToRGBA(color)
    colorArr.length == 3 && colorArr.push(1)
    let R = (1 - colorArr[3]) * 255 + colorArr[3] * colorArr[0]
    let G = (1 - colorArr[3]) * 255 + colorArr[3] * colorArr[1]
    let B = (1 - colorArr[3]) * 255 + colorArr[3] * colorArr[2]
    function calcBrightness(red,green,blue) {
        return Math.sqrt(red * red * .299 +
        green * green * .587 +
        blue * blue * .114);
    }
    let brightness = calcBrightness(R, G, B)
    return brightness > 220 ? '#000000' : '#ffffff';//浅色背景就返回深色文字颜色。
}

/**
 * 渲染星级评分
 * @param {number} star 评分数值，例如 3.5
 * @returns {string} 渲染好的 HTML 字符串
 */
function renderStars(star) {
    let starDomStr = "";

    let fullStars = Math.floor(star);
    let hasHalf = star % 1 >= 0.25 && star % 1 < 0.75;

    if (star % 1 >= 0.75) {
        fullStars += 1;
        hasHalf = false;
    }

    // 实心星
    for (let i = 0; i < fullStars; i++) {
        starDomStr += '<i class="bi bi-star-fill star i_good"></i>';
    }

    // 半星
    if (hasHalf) {
        starDomStr += '<i class="bi bi-star-half star i_good"></i>';
    }

    // 空星
    let remaining = 5 - fullStars - (hasHalf ? 1 : 0);
    for (let i = 0; i < remaining; i++) {
        starDomStr += '<i class="bi bi-star star i_good"></i>';
    }

    return starDomStr;
}