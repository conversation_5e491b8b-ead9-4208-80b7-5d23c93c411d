/* 页脚样式 - 从 index2.html 提取 */

/* 页脚变量定义 */
#bottom_64efe56731c1f {
    --footer-background-color: transparent;
    --footer-background-to: transparent;
    /* 背景颜色 */
    --footer-logo-size: 30px;
    /*logo字号*/
    --footer-logo-color: #000;
    /*logo字体颜色*/
    --footer-logo-font: "Arial";
    /*logo字体*/
    --footer-logo-italic: normal;
    /*logo倾斜*/
    --footer-logo-bold: 900;
    /*logo加粗*/
    --footer-logo-visible: block;
    /*logo显示隐藏*/

    --footer-title-size: 16px;
    /*标题字号*/
    --footer-title-color: #000;
    /*标题字体颜色*/
    --footer-title-font: "Arial";
    /*标题字体*/
    --footer-title-italic: normal;
    /*标题倾斜*/
    --footer-title-bold: 900;
    /*标题加粗*/
    --footer-title-hide: block;
    /*标题显示隐藏*/

    --footer-copyright-size: 14px;
    /*版权字号*/
    --footer-copyright-color: #666;
    /*版权字体颜色*/
    --footer-copyright-font: "Arial";
    /*版权字体*/
    --footer-copyright-italic: normal;
    /*版权倾斜*/
    --footer-copyright-bold: normal;
    /*版权加粗*/
    --footer-copyright-hide: block;
    /*版权显示隐藏*/

    --footer-links-size: 14px;
    /*链接字号*/
    --footer-links-color: #666;
    /*链接字体颜色*/
    --footer-links-font: "Arial";
    /*链接字体*/
    --footer-links-italic: normal;
    /*链接倾斜*/
    --footer-links-bold: normal;
    /*链接加粗*/
    --footer-links-hide: block;
    /*链接显示隐藏*/

    --footer-list-size: 14px;
    /*链接字号*/
    --footer-list-color: #666;
    /*链接字体颜色*/
    --footer-list-font: "Arial";
    /*链接字体*/
    --footer-list-italic: normal;
    /*链接倾斜*/
    --footer-list-bold: normal;
    /*链接加粗*/
    --footer-list-hide: block;
    /*链接显示隐藏*/

    --footer-media-hide: inline-block;
    /*链接显示隐藏*/
    --footer-payIcon-hide: inline-block;
    /*链接显示隐藏*/
    --footer-media-color: rgba(157, 25, 25, 1);
    /* 社交媒体颜色 */
    --footer-media-to: rgba(157, 25, 25, 1);
    /* 社交媒体颜色渐变色（暂时没用着） */
    /* 背景图片 */
    --footer-bgImg-cover: nothing;
    --footer-bgImg-color: transparent;
    --footer-bgImg-to: transparent;
    --footer-bgImg-hide: block;
}

/* 页脚基础样式 */
.footer_first .bgImage > div,
.footer_first .bgImageMobile > div {
    position: relative;
    z-index: 2;
}

/* 桌面端页脚背景 */
@media only screen and (min-width: 750px) {
    .footer_first .bgImage::before {
        content: '';
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, var(--footer-bgImg-color), var(--footer-bgImg-to));
        position: absolute;
        left: 0;
        top: 0;
        display: var(--footer-bgImg-hide);
        z-index: 1;
    }
    
    .footer_first .bgImage {
        position: relative;
        background: no-repeat center center / cover;
    }
}

/* 移动端页脚背景 */
@media only screen and (max-width: 749px) {
    .footer_first .bgImageMobile::before {
        content: '';
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, var(--footer-bgImg-color), var(--footer-bgImg-to));
        position: absolute;
        left: 0;
        top: 0;
        display: var(--footer-bgImg-hide);
        z-index: 1;
    }
    
    .footer_first .bgImageMobile {
        position: relative;
        background: no-repeat center center / cover;
    }
}
